/*
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

#pragma once

#include <react/runtime/ReactInstance.h>

namespace facebook::react {

class BindingsInstaller {
 public:
  virtual ReactInstance::BindingsInstallFunc getBindingsInstallFunc() {
    return nullptr;
  }
};

} // namespace facebook::react
