const fs = require('fs');
const path = require('path');

const assets = [
  'assets/icon.png',
  'assets/splash-icon.png',
  'assets/adaptive-icon.png'
];

let missingAssets = [];

assets.forEach(asset => {
  const assetPath = path.join(__dirname, '..', asset);
  if (!fs.existsSync(assetPath)) {
    missingAssets.push(asset);
  }
});

if (missingAssets.length > 0) {
  console.error('❌ Missing assets:');
  missingAssets.forEach(asset => console.error(`- ${asset}`));
  process.exit(1);
} else {
  console.log('✅ All assets exist');
  process.exit(0);
}