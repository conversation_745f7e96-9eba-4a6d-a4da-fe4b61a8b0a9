{"name": "livraison-afrique-mobile", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:development": "eas build --profile development", "build:preview": "eas build --profile preview", "build:staging": "eas build --profile staging", "build:production": "eas build --profile production", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "submit:production": "eas submit --profile production", "type-check": "tsc --noEmit", "lint": "eslint . --ext .ts,.tsx", "lint:fix": "eslint . --ext .ts,.tsx --fix", "prebuild": "expo prebuild --clean", "test": "echo \"Tests will be implemented in future iterations\"", "test:watch": "echo \"Test watch mode will be implemented\"", "security:audit": "npm audit --audit-level=high", "security:fix": "npm audit fix", "bundle:analyze": "npx expo export --platform web --output-dir dist && du -sh dist/", "db:migrate": "node scripts/run-migrations.js", "db:update-refs": "node scripts/update-references.js", "seed-db": "node scripts/seedDatabase.js", "clean-db": "node scripts/seedDatabase.js clean", "reset-db": "npm run clean-db && npm run seed-db", "deploy:staging": "npm run build:staging && npm run submit:staging", "deploy:production": "npm run build:production && npm run submit:production", "ci": "npm run type-check && npm run lint && npm run test && npm run security:audit", "fix-build": "node scripts/verify-build-config.js"}, "dependencies": {"@babel/plugin-transform-runtime": "^7.27.4", "@babel/preset-env": "^7.27.2", "@babel/runtime": "^7.27.6", "@expo/vector-icons": "^14.0.2", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^6.5.20", "@react-navigation/drawer": "^6.6.15", "@react-navigation/native": "^6.1.17", "@react-navigation/stack": "^6.3.29", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.40.0", "dotenv": "^16.5.0", "expo": "^54.0.0", "expo-application": "^6.1.4", "expo-av": "~15.1.6", "expo-build-properties": "^0.14.6", "expo-camera": "~16.1.6", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.4", "expo-dev-client": "~5.1.8", "expo-device": "~7.1.4", "expo-document-picker": "~12.0.2", "expo-font": "~13.3.1", "expo-haptics": "^14.1.4", "expo-image-picker": "~15.0.7", "expo-linear-gradient": "~14.1.4", "expo-location": "~18.1.5", "expo-network": "^7.1.5", "expo-notifications": "^0.31.2", "expo-sms": "~13.1.4", "expo-speech": "~13.1.7", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-animatable": "^1.4.0", "react-native-chart-kit": "^6.12.0", "react-native-gesture-handler": "~2.24.0", "react-native-maps": "1.20.1", "react-native-paper": "^5.12.3", "react-native-performance": "^5.1.4", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-vector-icons": "^10.1.0", "react-native-webview": "^13.15.0", "use-debounce": "^10.0.5", "uuid": "^11.1.0", "zustand": "^4.5.7", "expo-updates": "~0.28.15"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "^19.0.0", "@types/react-native": "^0.73.0", "@typescript-eslint/eslint-plugin": "^7.7.0", "@typescript-eslint/parser": "^7.7.0", "eas-cli": "^8.0.0", "eslint": "^8.57.0", "eslint-config-expo": "^7.0.0", "typescript": "~5.3.3"}, "private": true}