/**
 * This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
 *
 * Do not edit this file as changes may cause incorrect behavior and will be lost
 * once the code is regenerated.
 *
 * @generated by codegen project: GenerateModuleH.js
 */

#pragma once

#include <ReactCommon/TurboModule.h>
#include <react/bridging/Bridging.h>

namespace facebook::react {


  class JSI_EXPORT NativeReactNativeFeatureFlagsCxxSpecJSI : public TurboModule {
protected:
  NativeReactNativeFeatureFlagsCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual bool commonTestFlag(jsi::Runtime &rt) = 0;
  virtual bool commonTestFlagWithoutNativeImplementation(jsi::Runtime &rt) = 0;
  virtual bool disableMountItemReorderingAndroid(jsi::Runtime &rt) = 0;
  virtual bool enableAccumulatedUpdatesInRawPropsAndroid(jsi::Runtime &rt) = 0;
  virtual bool enableBridgelessArchitecture(jsi::Runtime &rt) = 0;
  virtual bool enableCppPropsIteratorSetter(jsi::Runtime &rt) = 0;
  virtual bool enableEagerRootViewAttachment(jsi::Runtime &rt) = 0;
  virtual bool enableFabricLogs(jsi::Runtime &rt) = 0;
  virtual bool enableFabricRenderer(jsi::Runtime &rt) = 0;
  virtual bool enableIOSViewClipToPaddingBox(jsi::Runtime &rt) = 0;
  virtual bool enableImagePrefetchingAndroid(jsi::Runtime &rt) = 0;
  virtual bool enableJSRuntimeGCOnMemoryPressureOnIOS(jsi::Runtime &rt) = 0;
  virtual bool enableLayoutAnimationsOnAndroid(jsi::Runtime &rt) = 0;
  virtual bool enableLayoutAnimationsOnIOS(jsi::Runtime &rt) = 0;
  virtual bool enableLongTaskAPI(jsi::Runtime &rt) = 0;
  virtual bool enableNativeCSSParsing(jsi::Runtime &rt) = 0;
  virtual bool enableNewBackgroundAndBorderDrawables(jsi::Runtime &rt) = 0;
  virtual bool enablePreciseSchedulingForPremountItemsOnAndroid(jsi::Runtime &rt) = 0;
  virtual bool enablePropsUpdateReconciliationAndroid(jsi::Runtime &rt) = 0;
  virtual bool enableReportEventPaintTime(jsi::Runtime &rt) = 0;
  virtual bool enableSynchronousStateUpdates(jsi::Runtime &rt) = 0;
  virtual bool enableUIConsistency(jsi::Runtime &rt) = 0;
  virtual bool enableViewCulling(jsi::Runtime &rt) = 0;
  virtual bool enableViewRecycling(jsi::Runtime &rt) = 0;
  virtual bool enableViewRecyclingForText(jsi::Runtime &rt) = 0;
  virtual bool enableViewRecyclingForView(jsi::Runtime &rt) = 0;
  virtual bool excludeYogaFromRawProps(jsi::Runtime &rt) = 0;
  virtual bool fixDifferentiatorEmittingUpdatesWithWrongParentTag(jsi::Runtime &rt) = 0;
  virtual bool fixMappingOfEventPrioritiesBetweenFabricAndReact(jsi::Runtime &rt) = 0;
  virtual bool fixMountingCoordinatorReportedPendingTransactionsOnAndroid(jsi::Runtime &rt) = 0;
  virtual bool fuseboxEnabledRelease(jsi::Runtime &rt) = 0;
  virtual bool fuseboxNetworkInspectionEnabled(jsi::Runtime &rt) = 0;
  virtual bool lazyAnimationCallbacks(jsi::Runtime &rt) = 0;
  virtual bool removeTurboModuleManagerDelegateMutex(jsi::Runtime &rt) = 0;
  virtual bool throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS(jsi::Runtime &rt) = 0;
  virtual bool traceTurboModulePromiseRejectionsOnAndroid(jsi::Runtime &rt) = 0;
  virtual bool updateRuntimeShadowNodeReferencesOnCommit(jsi::Runtime &rt) = 0;
  virtual bool useAlwaysAvailableJSErrorHandling(jsi::Runtime &rt) = 0;
  virtual bool useEditTextStockAndroidFocusBehavior(jsi::Runtime &rt) = 0;
  virtual bool useFabricInterop(jsi::Runtime &rt) = 0;
  virtual bool useNativeViewConfigsInBridgelessMode(jsi::Runtime &rt) = 0;
  virtual bool useOptimizedEventBatchingOnAndroid(jsi::Runtime &rt) = 0;
  virtual bool useRawPropsJsiValue(jsi::Runtime &rt) = 0;
  virtual bool useShadowNodeStateOnClone(jsi::Runtime &rt) = 0;
  virtual bool useTurboModuleInterop(jsi::Runtime &rt) = 0;
  virtual bool useTurboModules(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeReactNativeFeatureFlagsCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "NativeReactNativeFeatureFlagsCxx";

protected:
  NativeReactNativeFeatureFlagsCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeReactNativeFeatureFlagsCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeReactNativeFeatureFlagsCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeReactNativeFeatureFlagsCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    bool commonTestFlag(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::commonTestFlag) == 1,
          "Expected commonTestFlag(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::commonTestFlag, jsInvoker_, instance_);
    }
    bool commonTestFlagWithoutNativeImplementation(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::commonTestFlagWithoutNativeImplementation) == 1,
          "Expected commonTestFlagWithoutNativeImplementation(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::commonTestFlagWithoutNativeImplementation, jsInvoker_, instance_);
    }
    bool disableMountItemReorderingAndroid(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::disableMountItemReorderingAndroid) == 1,
          "Expected disableMountItemReorderingAndroid(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::disableMountItemReorderingAndroid, jsInvoker_, instance_);
    }
    bool enableAccumulatedUpdatesInRawPropsAndroid(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableAccumulatedUpdatesInRawPropsAndroid) == 1,
          "Expected enableAccumulatedUpdatesInRawPropsAndroid(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableAccumulatedUpdatesInRawPropsAndroid, jsInvoker_, instance_);
    }
    bool enableBridgelessArchitecture(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableBridgelessArchitecture) == 1,
          "Expected enableBridgelessArchitecture(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableBridgelessArchitecture, jsInvoker_, instance_);
    }
    bool enableCppPropsIteratorSetter(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableCppPropsIteratorSetter) == 1,
          "Expected enableCppPropsIteratorSetter(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableCppPropsIteratorSetter, jsInvoker_, instance_);
    }
    bool enableEagerRootViewAttachment(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableEagerRootViewAttachment) == 1,
          "Expected enableEagerRootViewAttachment(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableEagerRootViewAttachment, jsInvoker_, instance_);
    }
    bool enableFabricLogs(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableFabricLogs) == 1,
          "Expected enableFabricLogs(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableFabricLogs, jsInvoker_, instance_);
    }
    bool enableFabricRenderer(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableFabricRenderer) == 1,
          "Expected enableFabricRenderer(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableFabricRenderer, jsInvoker_, instance_);
    }
    bool enableIOSViewClipToPaddingBox(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableIOSViewClipToPaddingBox) == 1,
          "Expected enableIOSViewClipToPaddingBox(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableIOSViewClipToPaddingBox, jsInvoker_, instance_);
    }
    bool enableImagePrefetchingAndroid(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableImagePrefetchingAndroid) == 1,
          "Expected enableImagePrefetchingAndroid(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableImagePrefetchingAndroid, jsInvoker_, instance_);
    }
    bool enableJSRuntimeGCOnMemoryPressureOnIOS(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableJSRuntimeGCOnMemoryPressureOnIOS) == 1,
          "Expected enableJSRuntimeGCOnMemoryPressureOnIOS(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableJSRuntimeGCOnMemoryPressureOnIOS, jsInvoker_, instance_);
    }
    bool enableLayoutAnimationsOnAndroid(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableLayoutAnimationsOnAndroid) == 1,
          "Expected enableLayoutAnimationsOnAndroid(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableLayoutAnimationsOnAndroid, jsInvoker_, instance_);
    }
    bool enableLayoutAnimationsOnIOS(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableLayoutAnimationsOnIOS) == 1,
          "Expected enableLayoutAnimationsOnIOS(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableLayoutAnimationsOnIOS, jsInvoker_, instance_);
    }
    bool enableLongTaskAPI(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableLongTaskAPI) == 1,
          "Expected enableLongTaskAPI(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableLongTaskAPI, jsInvoker_, instance_);
    }
    bool enableNativeCSSParsing(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableNativeCSSParsing) == 1,
          "Expected enableNativeCSSParsing(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableNativeCSSParsing, jsInvoker_, instance_);
    }
    bool enableNewBackgroundAndBorderDrawables(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableNewBackgroundAndBorderDrawables) == 1,
          "Expected enableNewBackgroundAndBorderDrawables(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableNewBackgroundAndBorderDrawables, jsInvoker_, instance_);
    }
    bool enablePreciseSchedulingForPremountItemsOnAndroid(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enablePreciseSchedulingForPremountItemsOnAndroid) == 1,
          "Expected enablePreciseSchedulingForPremountItemsOnAndroid(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enablePreciseSchedulingForPremountItemsOnAndroid, jsInvoker_, instance_);
    }
    bool enablePropsUpdateReconciliationAndroid(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enablePropsUpdateReconciliationAndroid) == 1,
          "Expected enablePropsUpdateReconciliationAndroid(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enablePropsUpdateReconciliationAndroid, jsInvoker_, instance_);
    }
    bool enableReportEventPaintTime(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableReportEventPaintTime) == 1,
          "Expected enableReportEventPaintTime(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableReportEventPaintTime, jsInvoker_, instance_);
    }
    bool enableSynchronousStateUpdates(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableSynchronousStateUpdates) == 1,
          "Expected enableSynchronousStateUpdates(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableSynchronousStateUpdates, jsInvoker_, instance_);
    }
    bool enableUIConsistency(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableUIConsistency) == 1,
          "Expected enableUIConsistency(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableUIConsistency, jsInvoker_, instance_);
    }
    bool enableViewCulling(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableViewCulling) == 1,
          "Expected enableViewCulling(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableViewCulling, jsInvoker_, instance_);
    }
    bool enableViewRecycling(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableViewRecycling) == 1,
          "Expected enableViewRecycling(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableViewRecycling, jsInvoker_, instance_);
    }
    bool enableViewRecyclingForText(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableViewRecyclingForText) == 1,
          "Expected enableViewRecyclingForText(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableViewRecyclingForText, jsInvoker_, instance_);
    }
    bool enableViewRecyclingForView(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::enableViewRecyclingForView) == 1,
          "Expected enableViewRecyclingForView(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::enableViewRecyclingForView, jsInvoker_, instance_);
    }
    bool excludeYogaFromRawProps(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::excludeYogaFromRawProps) == 1,
          "Expected excludeYogaFromRawProps(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::excludeYogaFromRawProps, jsInvoker_, instance_);
    }
    bool fixDifferentiatorEmittingUpdatesWithWrongParentTag(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::fixDifferentiatorEmittingUpdatesWithWrongParentTag) == 1,
          "Expected fixDifferentiatorEmittingUpdatesWithWrongParentTag(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::fixDifferentiatorEmittingUpdatesWithWrongParentTag, jsInvoker_, instance_);
    }
    bool fixMappingOfEventPrioritiesBetweenFabricAndReact(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::fixMappingOfEventPrioritiesBetweenFabricAndReact) == 1,
          "Expected fixMappingOfEventPrioritiesBetweenFabricAndReact(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::fixMappingOfEventPrioritiesBetweenFabricAndReact, jsInvoker_, instance_);
    }
    bool fixMountingCoordinatorReportedPendingTransactionsOnAndroid(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::fixMountingCoordinatorReportedPendingTransactionsOnAndroid) == 1,
          "Expected fixMountingCoordinatorReportedPendingTransactionsOnAndroid(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::fixMountingCoordinatorReportedPendingTransactionsOnAndroid, jsInvoker_, instance_);
    }
    bool fuseboxEnabledRelease(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::fuseboxEnabledRelease) == 1,
          "Expected fuseboxEnabledRelease(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::fuseboxEnabledRelease, jsInvoker_, instance_);
    }
    bool fuseboxNetworkInspectionEnabled(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::fuseboxNetworkInspectionEnabled) == 1,
          "Expected fuseboxNetworkInspectionEnabled(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::fuseboxNetworkInspectionEnabled, jsInvoker_, instance_);
    }
    bool lazyAnimationCallbacks(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::lazyAnimationCallbacks) == 1,
          "Expected lazyAnimationCallbacks(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::lazyAnimationCallbacks, jsInvoker_, instance_);
    }
    bool removeTurboModuleManagerDelegateMutex(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::removeTurboModuleManagerDelegateMutex) == 1,
          "Expected removeTurboModuleManagerDelegateMutex(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::removeTurboModuleManagerDelegateMutex, jsInvoker_, instance_);
    }
    bool throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS) == 1,
          "Expected throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::throwExceptionInsteadOfDeadlockOnTurboModuleSetupDuringSyncRenderIOS, jsInvoker_, instance_);
    }
    bool traceTurboModulePromiseRejectionsOnAndroid(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::traceTurboModulePromiseRejectionsOnAndroid) == 1,
          "Expected traceTurboModulePromiseRejectionsOnAndroid(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::traceTurboModulePromiseRejectionsOnAndroid, jsInvoker_, instance_);
    }
    bool updateRuntimeShadowNodeReferencesOnCommit(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::updateRuntimeShadowNodeReferencesOnCommit) == 1,
          "Expected updateRuntimeShadowNodeReferencesOnCommit(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::updateRuntimeShadowNodeReferencesOnCommit, jsInvoker_, instance_);
    }
    bool useAlwaysAvailableJSErrorHandling(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::useAlwaysAvailableJSErrorHandling) == 1,
          "Expected useAlwaysAvailableJSErrorHandling(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::useAlwaysAvailableJSErrorHandling, jsInvoker_, instance_);
    }
    bool useEditTextStockAndroidFocusBehavior(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::useEditTextStockAndroidFocusBehavior) == 1,
          "Expected useEditTextStockAndroidFocusBehavior(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::useEditTextStockAndroidFocusBehavior, jsInvoker_, instance_);
    }
    bool useFabricInterop(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::useFabricInterop) == 1,
          "Expected useFabricInterop(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::useFabricInterop, jsInvoker_, instance_);
    }
    bool useNativeViewConfigsInBridgelessMode(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::useNativeViewConfigsInBridgelessMode) == 1,
          "Expected useNativeViewConfigsInBridgelessMode(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::useNativeViewConfigsInBridgelessMode, jsInvoker_, instance_);
    }
    bool useOptimizedEventBatchingOnAndroid(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::useOptimizedEventBatchingOnAndroid) == 1,
          "Expected useOptimizedEventBatchingOnAndroid(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::useOptimizedEventBatchingOnAndroid, jsInvoker_, instance_);
    }
    bool useRawPropsJsiValue(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::useRawPropsJsiValue) == 1,
          "Expected useRawPropsJsiValue(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::useRawPropsJsiValue, jsInvoker_, instance_);
    }
    bool useShadowNodeStateOnClone(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::useShadowNodeStateOnClone) == 1,
          "Expected useShadowNodeStateOnClone(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::useShadowNodeStateOnClone, jsInvoker_, instance_);
    }
    bool useTurboModuleInterop(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::useTurboModuleInterop) == 1,
          "Expected useTurboModuleInterop(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::useTurboModuleInterop, jsInvoker_, instance_);
    }
    bool useTurboModules(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::useTurboModules) == 1,
          "Expected useTurboModules(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::useTurboModules, jsInvoker_, instance_);
    }

  private:
    friend class NativeReactNativeFeatureFlagsCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeReactDevToolsRuntimeSettingsModulePartialReloadAndProfileConfig

template <typename P0, typename P1>
struct NativeReactDevToolsRuntimeSettingsModulePartialReloadAndProfileConfig {
  P0 shouldReloadAndProfile;
  P1 recordChangeDescriptions;
  bool operator==(const NativeReactDevToolsRuntimeSettingsModulePartialReloadAndProfileConfig &other) const {
    return shouldReloadAndProfile == other.shouldReloadAndProfile && recordChangeDescriptions == other.recordChangeDescriptions;
  }
};

template <typename T>
struct NativeReactDevToolsRuntimeSettingsModulePartialReloadAndProfileConfigBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.shouldReloadAndProfile)>(rt, value.getProperty(rt, "shouldReloadAndProfile"), jsInvoker),
      bridging::fromJs<decltype(types.recordChangeDescriptions)>(rt, value.getProperty(rt, "recordChangeDescriptions"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static bool shouldReloadAndProfileToJs(jsi::Runtime &rt, decltype(types.shouldReloadAndProfile) value) {
    return bridging::toJs(rt, value);
  }

  static bool recordChangeDescriptionsToJs(jsi::Runtime &rt, decltype(types.recordChangeDescriptions) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    if (value.shouldReloadAndProfile) {
      result.setProperty(rt, "shouldReloadAndProfile", bridging::toJs(rt, value.shouldReloadAndProfile.value(), jsInvoker));
    }
    if (value.recordChangeDescriptions) {
      result.setProperty(rt, "recordChangeDescriptions", bridging::toJs(rt, value.recordChangeDescriptions.value(), jsInvoker));
    }
    return result;
  }
};



#pragma mark - NativeReactDevToolsRuntimeSettingsModuleReloadAndProfileConfig

template <typename P0, typename P1>
struct NativeReactDevToolsRuntimeSettingsModuleReloadAndProfileConfig {
  P0 shouldReloadAndProfile;
  P1 recordChangeDescriptions;
  bool operator==(const NativeReactDevToolsRuntimeSettingsModuleReloadAndProfileConfig &other) const {
    return shouldReloadAndProfile == other.shouldReloadAndProfile && recordChangeDescriptions == other.recordChangeDescriptions;
  }
};

template <typename T>
struct NativeReactDevToolsRuntimeSettingsModuleReloadAndProfileConfigBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.shouldReloadAndProfile)>(rt, value.getProperty(rt, "shouldReloadAndProfile"), jsInvoker),
      bridging::fromJs<decltype(types.recordChangeDescriptions)>(rt, value.getProperty(rt, "recordChangeDescriptions"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static bool shouldReloadAndProfileToJs(jsi::Runtime &rt, decltype(types.shouldReloadAndProfile) value) {
    return bridging::toJs(rt, value);
  }

  static bool recordChangeDescriptionsToJs(jsi::Runtime &rt, decltype(types.recordChangeDescriptions) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "shouldReloadAndProfile", bridging::toJs(rt, value.shouldReloadAndProfile, jsInvoker));
    result.setProperty(rt, "recordChangeDescriptions", bridging::toJs(rt, value.recordChangeDescriptions, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativeReactDevToolsRuntimeSettingsModuleCxxSpecJSI : public TurboModule {
protected:
  NativeReactDevToolsRuntimeSettingsModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void setReloadAndProfileConfig(jsi::Runtime &rt, jsi::Object config) = 0;
  virtual jsi::Object getReloadAndProfileConfig(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeReactDevToolsRuntimeSettingsModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "ReactDevToolsRuntimeSettingsModule";

protected:
  NativeReactDevToolsRuntimeSettingsModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeReactDevToolsRuntimeSettingsModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeReactDevToolsRuntimeSettingsModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeReactDevToolsRuntimeSettingsModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void setReloadAndProfileConfig(jsi::Runtime &rt, jsi::Object config) override {
      static_assert(
          bridging::getParameterCount(&T::setReloadAndProfileConfig) == 2,
          "Expected setReloadAndProfileConfig(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setReloadAndProfileConfig, jsInvoker_, instance_, std::move(config));
    }
    jsi::Object getReloadAndProfileConfig(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getReloadAndProfileConfig) == 1,
          "Expected getReloadAndProfileConfig(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getReloadAndProfileConfig, jsInvoker_, instance_);
    }

  private:
    friend class NativeReactDevToolsRuntimeSettingsModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeAccessibilityInfoCxxSpecJSI : public TurboModule {
protected:
  NativeAccessibilityInfoCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void isReduceMotionEnabled(jsi::Runtime &rt, jsi::Function onSuccess) = 0;
  virtual void isInvertColorsEnabled(jsi::Runtime &rt, jsi::Function onSuccess) = 0;
  virtual void isHighTextContrastEnabled(jsi::Runtime &rt, jsi::Function onSuccess) = 0;
  virtual void isTouchExplorationEnabled(jsi::Runtime &rt, jsi::Function onSuccess) = 0;
  virtual void isAccessibilityServiceEnabled(jsi::Runtime &rt, jsi::Function onSuccess) = 0;
  virtual void setAccessibilityFocus(jsi::Runtime &rt, double reactTag) = 0;
  virtual void announceForAccessibility(jsi::Runtime &rt, jsi::String announcement) = 0;
  virtual void getRecommendedTimeoutMillis(jsi::Runtime &rt, double mSec, jsi::Function onSuccess) = 0;
  virtual void isGrayscaleEnabled(jsi::Runtime &rt, jsi::Function onSuccess) = 0;

};

template <typename T>
class JSI_EXPORT NativeAccessibilityInfoCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "AccessibilityInfo";

protected:
  NativeAccessibilityInfoCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeAccessibilityInfoCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeAccessibilityInfoCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeAccessibilityInfoCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void isReduceMotionEnabled(jsi::Runtime &rt, jsi::Function onSuccess) override {
      static_assert(
          bridging::getParameterCount(&T::isReduceMotionEnabled) == 2,
          "Expected isReduceMotionEnabled(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::isReduceMotionEnabled, jsInvoker_, instance_, std::move(onSuccess));
    }
    void isInvertColorsEnabled(jsi::Runtime &rt, jsi::Function onSuccess) override {
      static_assert(
          bridging::getParameterCount(&T::isInvertColorsEnabled) == 2,
          "Expected isInvertColorsEnabled(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::isInvertColorsEnabled, jsInvoker_, instance_, std::move(onSuccess));
    }
    void isHighTextContrastEnabled(jsi::Runtime &rt, jsi::Function onSuccess) override {
      static_assert(
          bridging::getParameterCount(&T::isHighTextContrastEnabled) == 2,
          "Expected isHighTextContrastEnabled(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::isHighTextContrastEnabled, jsInvoker_, instance_, std::move(onSuccess));
    }
    void isTouchExplorationEnabled(jsi::Runtime &rt, jsi::Function onSuccess) override {
      static_assert(
          bridging::getParameterCount(&T::isTouchExplorationEnabled) == 2,
          "Expected isTouchExplorationEnabled(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::isTouchExplorationEnabled, jsInvoker_, instance_, std::move(onSuccess));
    }
    void isAccessibilityServiceEnabled(jsi::Runtime &rt, jsi::Function onSuccess) override {
      static_assert(
          bridging::getParameterCount(&T::isAccessibilityServiceEnabled) == 2,
          "Expected isAccessibilityServiceEnabled(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::isAccessibilityServiceEnabled, jsInvoker_, instance_, std::move(onSuccess));
    }
    void setAccessibilityFocus(jsi::Runtime &rt, double reactTag) override {
      static_assert(
          bridging::getParameterCount(&T::setAccessibilityFocus) == 2,
          "Expected setAccessibilityFocus(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setAccessibilityFocus, jsInvoker_, instance_, std::move(reactTag));
    }
    void announceForAccessibility(jsi::Runtime &rt, jsi::String announcement) override {
      static_assert(
          bridging::getParameterCount(&T::announceForAccessibility) == 2,
          "Expected announceForAccessibility(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::announceForAccessibility, jsInvoker_, instance_, std::move(announcement));
    }
    void getRecommendedTimeoutMillis(jsi::Runtime &rt, double mSec, jsi::Function onSuccess) override {
      static_assert(
          bridging::getParameterCount(&T::getRecommendedTimeoutMillis) == 3,
          "Expected getRecommendedTimeoutMillis(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getRecommendedTimeoutMillis, jsInvoker_, instance_, std::move(mSec), std::move(onSuccess));
    }
    void isGrayscaleEnabled(jsi::Runtime &rt, jsi::Function onSuccess) override {
      static_assert(
          bridging::getParameterCount(&T::isGrayscaleEnabled) == 2,
          "Expected isGrayscaleEnabled(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::isGrayscaleEnabled, jsInvoker_, instance_, std::move(onSuccess));
    }

  private:
    friend class NativeAccessibilityInfoCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeAccessibilityManagerCxxSpecJSI : public TurboModule {
protected:
  NativeAccessibilityManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void getCurrentBoldTextState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) = 0;
  virtual void getCurrentGrayscaleState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) = 0;
  virtual void getCurrentInvertColorsState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) = 0;
  virtual void getCurrentReduceMotionState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) = 0;
  virtual void getCurrentDarkerSystemColorsState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) = 0;
  virtual void getCurrentPrefersCrossFadeTransitionsState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) = 0;
  virtual void getCurrentReduceTransparencyState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) = 0;
  virtual void getCurrentVoiceOverState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) = 0;
  virtual void setAccessibilityContentSizeMultipliers(jsi::Runtime &rt, jsi::Object JSMultipliers) = 0;
  virtual void setAccessibilityFocus(jsi::Runtime &rt, double reactTag) = 0;
  virtual void announceForAccessibility(jsi::Runtime &rt, jsi::String announcement) = 0;
  virtual void announceForAccessibilityWithOptions(jsi::Runtime &rt, jsi::String announcement, jsi::Object options) = 0;

};

template <typename T>
class JSI_EXPORT NativeAccessibilityManagerCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "AccessibilityManager";

protected:
  NativeAccessibilityManagerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeAccessibilityManagerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeAccessibilityManagerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeAccessibilityManagerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void getCurrentBoldTextState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) override {
      static_assert(
          bridging::getParameterCount(&T::getCurrentBoldTextState) == 3,
          "Expected getCurrentBoldTextState(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getCurrentBoldTextState, jsInvoker_, instance_, std::move(onSuccess), std::move(onError));
    }
    void getCurrentGrayscaleState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) override {
      static_assert(
          bridging::getParameterCount(&T::getCurrentGrayscaleState) == 3,
          "Expected getCurrentGrayscaleState(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getCurrentGrayscaleState, jsInvoker_, instance_, std::move(onSuccess), std::move(onError));
    }
    void getCurrentInvertColorsState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) override {
      static_assert(
          bridging::getParameterCount(&T::getCurrentInvertColorsState) == 3,
          "Expected getCurrentInvertColorsState(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getCurrentInvertColorsState, jsInvoker_, instance_, std::move(onSuccess), std::move(onError));
    }
    void getCurrentReduceMotionState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) override {
      static_assert(
          bridging::getParameterCount(&T::getCurrentReduceMotionState) == 3,
          "Expected getCurrentReduceMotionState(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getCurrentReduceMotionState, jsInvoker_, instance_, std::move(onSuccess), std::move(onError));
    }
    void getCurrentDarkerSystemColorsState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) override {
      static_assert(
          bridging::getParameterCount(&T::getCurrentDarkerSystemColorsState) == 3,
          "Expected getCurrentDarkerSystemColorsState(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getCurrentDarkerSystemColorsState, jsInvoker_, instance_, std::move(onSuccess), std::move(onError));
    }
    void getCurrentPrefersCrossFadeTransitionsState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) override {
      static_assert(
          bridging::getParameterCount(&T::getCurrentPrefersCrossFadeTransitionsState) == 3,
          "Expected getCurrentPrefersCrossFadeTransitionsState(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getCurrentPrefersCrossFadeTransitionsState, jsInvoker_, instance_, std::move(onSuccess), std::move(onError));
    }
    void getCurrentReduceTransparencyState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) override {
      static_assert(
          bridging::getParameterCount(&T::getCurrentReduceTransparencyState) == 3,
          "Expected getCurrentReduceTransparencyState(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getCurrentReduceTransparencyState, jsInvoker_, instance_, std::move(onSuccess), std::move(onError));
    }
    void getCurrentVoiceOverState(jsi::Runtime &rt, jsi::Function onSuccess, jsi::Function onError) override {
      static_assert(
          bridging::getParameterCount(&T::getCurrentVoiceOverState) == 3,
          "Expected getCurrentVoiceOverState(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getCurrentVoiceOverState, jsInvoker_, instance_, std::move(onSuccess), std::move(onError));
    }
    void setAccessibilityContentSizeMultipliers(jsi::Runtime &rt, jsi::Object JSMultipliers) override {
      static_assert(
          bridging::getParameterCount(&T::setAccessibilityContentSizeMultipliers) == 2,
          "Expected setAccessibilityContentSizeMultipliers(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setAccessibilityContentSizeMultipliers, jsInvoker_, instance_, std::move(JSMultipliers));
    }
    void setAccessibilityFocus(jsi::Runtime &rt, double reactTag) override {
      static_assert(
          bridging::getParameterCount(&T::setAccessibilityFocus) == 2,
          "Expected setAccessibilityFocus(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setAccessibilityFocus, jsInvoker_, instance_, std::move(reactTag));
    }
    void announceForAccessibility(jsi::Runtime &rt, jsi::String announcement) override {
      static_assert(
          bridging::getParameterCount(&T::announceForAccessibility) == 2,
          "Expected announceForAccessibility(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::announceForAccessibility, jsInvoker_, instance_, std::move(announcement));
    }
    void announceForAccessibilityWithOptions(jsi::Runtime &rt, jsi::String announcement, jsi::Object options) override {
      static_assert(
          bridging::getParameterCount(&T::announceForAccessibilityWithOptions) == 3,
          "Expected announceForAccessibilityWithOptions(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::announceForAccessibilityWithOptions, jsInvoker_, instance_, std::move(announcement), std::move(options));
    }

  private:
    friend class NativeAccessibilityManagerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeActionSheetManagerCxxSpecJSI : public TurboModule {
protected:
  NativeActionSheetManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual void showActionSheetWithOptions(jsi::Runtime &rt, jsi::Object options, jsi::Function callback) = 0;
  virtual void showShareActionSheetWithOptions(jsi::Runtime &rt, jsi::Object options, jsi::Function failureCallback, jsi::Function successCallback) = 0;
  virtual void dismissActionSheet(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeActionSheetManagerCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "ActionSheetManager";

protected:
  NativeActionSheetManagerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeActionSheetManagerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeActionSheetManagerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeActionSheetManagerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    void showActionSheetWithOptions(jsi::Runtime &rt, jsi::Object options, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::showActionSheetWithOptions) == 3,
          "Expected showActionSheetWithOptions(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::showActionSheetWithOptions, jsInvoker_, instance_, std::move(options), std::move(callback));
    }
    void showShareActionSheetWithOptions(jsi::Runtime &rt, jsi::Object options, jsi::Function failureCallback, jsi::Function successCallback) override {
      static_assert(
          bridging::getParameterCount(&T::showShareActionSheetWithOptions) == 4,
          "Expected showShareActionSheetWithOptions(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::showShareActionSheetWithOptions, jsInvoker_, instance_, std::move(options), std::move(failureCallback), std::move(successCallback));
    }
    void dismissActionSheet(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::dismissActionSheet) == 1,
          "Expected dismissActionSheet(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::dismissActionSheet, jsInvoker_, instance_);
    }

  private:
    friend class NativeActionSheetManagerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeAlertManagerArgs

template <typename P0, typename P1, typename P2, typename P3, typename P4, typename P5, typename P6, typename P7, typename P8, typename P9>
struct NativeAlertManagerArgs {
  P0 title;
  P1 message;
  P2 buttons;
  P3 type;
  P4 defaultValue;
  P5 cancelButtonKey;
  P6 destructiveButtonKey;
  P7 preferredButtonKey;
  P8 keyboardType;
  P9 userInterfaceStyle;
  bool operator==(const NativeAlertManagerArgs &other) const {
    return title == other.title && message == other.message && buttons == other.buttons && type == other.type && defaultValue == other.defaultValue && cancelButtonKey == other.cancelButtonKey && destructiveButtonKey == other.destructiveButtonKey && preferredButtonKey == other.preferredButtonKey && keyboardType == other.keyboardType && userInterfaceStyle == other.userInterfaceStyle;
  }
};

template <typename T>
struct NativeAlertManagerArgsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.title)>(rt, value.getProperty(rt, "title"), jsInvoker),
      bridging::fromJs<decltype(types.message)>(rt, value.getProperty(rt, "message"), jsInvoker),
      bridging::fromJs<decltype(types.buttons)>(rt, value.getProperty(rt, "buttons"), jsInvoker),
      bridging::fromJs<decltype(types.type)>(rt, value.getProperty(rt, "type"), jsInvoker),
      bridging::fromJs<decltype(types.defaultValue)>(rt, value.getProperty(rt, "defaultValue"), jsInvoker),
      bridging::fromJs<decltype(types.cancelButtonKey)>(rt, value.getProperty(rt, "cancelButtonKey"), jsInvoker),
      bridging::fromJs<decltype(types.destructiveButtonKey)>(rt, value.getProperty(rt, "destructiveButtonKey"), jsInvoker),
      bridging::fromJs<decltype(types.preferredButtonKey)>(rt, value.getProperty(rt, "preferredButtonKey"), jsInvoker),
      bridging::fromJs<decltype(types.keyboardType)>(rt, value.getProperty(rt, "keyboardType"), jsInvoker),
      bridging::fromJs<decltype(types.userInterfaceStyle)>(rt, value.getProperty(rt, "userInterfaceStyle"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::String titleToJs(jsi::Runtime &rt, decltype(types.title) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String messageToJs(jsi::Runtime &rt, decltype(types.message) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Array buttonsToJs(jsi::Runtime &rt, decltype(types.buttons) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String typeToJs(jsi::Runtime &rt, decltype(types.type) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String defaultValueToJs(jsi::Runtime &rt, decltype(types.defaultValue) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String cancelButtonKeyToJs(jsi::Runtime &rt, decltype(types.cancelButtonKey) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String destructiveButtonKeyToJs(jsi::Runtime &rt, decltype(types.destructiveButtonKey) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String preferredButtonKeyToJs(jsi::Runtime &rt, decltype(types.preferredButtonKey) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String keyboardTypeToJs(jsi::Runtime &rt, decltype(types.keyboardType) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String userInterfaceStyleToJs(jsi::Runtime &rt, decltype(types.userInterfaceStyle) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    if (value.title) {
      result.setProperty(rt, "title", bridging::toJs(rt, value.title.value(), jsInvoker));
    }
    if (value.message) {
      result.setProperty(rt, "message", bridging::toJs(rt, value.message.value(), jsInvoker));
    }
    if (value.buttons) {
      result.setProperty(rt, "buttons", bridging::toJs(rt, value.buttons.value(), jsInvoker));
    }
    if (value.type) {
      result.setProperty(rt, "type", bridging::toJs(rt, value.type.value(), jsInvoker));
    }
    if (value.defaultValue) {
      result.setProperty(rt, "defaultValue", bridging::toJs(rt, value.defaultValue.value(), jsInvoker));
    }
    if (value.cancelButtonKey) {
      result.setProperty(rt, "cancelButtonKey", bridging::toJs(rt, value.cancelButtonKey.value(), jsInvoker));
    }
    if (value.destructiveButtonKey) {
      result.setProperty(rt, "destructiveButtonKey", bridging::toJs(rt, value.destructiveButtonKey.value(), jsInvoker));
    }
    if (value.preferredButtonKey) {
      result.setProperty(rt, "preferredButtonKey", bridging::toJs(rt, value.preferredButtonKey.value(), jsInvoker));
    }
    if (value.keyboardType) {
      result.setProperty(rt, "keyboardType", bridging::toJs(rt, value.keyboardType.value(), jsInvoker));
    }
    if (value.userInterfaceStyle) {
      result.setProperty(rt, "userInterfaceStyle", bridging::toJs(rt, value.userInterfaceStyle.value(), jsInvoker));
    }
    return result;
  }
};

class JSI_EXPORT NativeAlertManagerCxxSpecJSI : public TurboModule {
protected:
  NativeAlertManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void alertWithArgs(jsi::Runtime &rt, jsi::Object args, jsi::Function callback) = 0;

};

template <typename T>
class JSI_EXPORT NativeAlertManagerCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "AlertManager";

protected:
  NativeAlertManagerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeAlertManagerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeAlertManagerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeAlertManagerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void alertWithArgs(jsi::Runtime &rt, jsi::Object args, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::alertWithArgs) == 3,
          "Expected alertWithArgs(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::alertWithArgs, jsInvoker_, instance_, std::move(args), std::move(callback));
    }

  private:
    friend class NativeAlertManagerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeAnimatedModuleEndResult

template <typename P0, typename P1>
struct NativeAnimatedModuleEndResult {
  P0 finished;
  P1 value;
  bool operator==(const NativeAnimatedModuleEndResult &other) const {
    return finished == other.finished && value == other.value;
  }
};

template <typename T>
struct NativeAnimatedModuleEndResultBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.finished)>(rt, value.getProperty(rt, "finished"), jsInvoker),
      bridging::fromJs<decltype(types.value)>(rt, value.getProperty(rt, "value"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static bool finishedToJs(jsi::Runtime &rt, decltype(types.finished) value) {
    return bridging::toJs(rt, value);
  }

  static double valueToJs(jsi::Runtime &rt, decltype(types.value) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "finished", bridging::toJs(rt, value.finished, jsInvoker));
    if (value.value) {
      result.setProperty(rt, "value", bridging::toJs(rt, value.value.value(), jsInvoker));
    }
    return result;
  }
};



#pragma mark - NativeAnimatedModuleEventMapping

template <typename P0, typename P1>
struct NativeAnimatedModuleEventMapping {
  P0 nativeEventPath;
  P1 animatedValueTag;
  bool operator==(const NativeAnimatedModuleEventMapping &other) const {
    return nativeEventPath == other.nativeEventPath && animatedValueTag == other.animatedValueTag;
  }
};

template <typename T>
struct NativeAnimatedModuleEventMappingBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.nativeEventPath)>(rt, value.getProperty(rt, "nativeEventPath"), jsInvoker),
      bridging::fromJs<decltype(types.animatedValueTag)>(rt, value.getProperty(rt, "animatedValueTag"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::Array nativeEventPathToJs(jsi::Runtime &rt, decltype(types.nativeEventPath) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<double> animatedValueTagToJs(jsi::Runtime &rt, decltype(types.animatedValueTag) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "nativeEventPath", bridging::toJs(rt, value.nativeEventPath, jsInvoker));
    result.setProperty(rt, "animatedValueTag", bridging::toJs(rt, value.animatedValueTag, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativeAnimatedModuleCxxSpecJSI : public TurboModule {
protected:
  NativeAnimatedModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void startOperationBatch(jsi::Runtime &rt) = 0;
  virtual void finishOperationBatch(jsi::Runtime &rt) = 0;
  virtual void createAnimatedNode(jsi::Runtime &rt, double tag, jsi::Object config) = 0;
  virtual void updateAnimatedNodeConfig(jsi::Runtime &rt, double tag, jsi::Object config) = 0;
  virtual void getValue(jsi::Runtime &rt, double tag, jsi::Function saveValueCallback) = 0;
  virtual void startListeningToAnimatedNodeValue(jsi::Runtime &rt, double tag) = 0;
  virtual void stopListeningToAnimatedNodeValue(jsi::Runtime &rt, double tag) = 0;
  virtual void connectAnimatedNodes(jsi::Runtime &rt, double parentTag, double childTag) = 0;
  virtual void disconnectAnimatedNodes(jsi::Runtime &rt, double parentTag, double childTag) = 0;
  virtual void startAnimatingNode(jsi::Runtime &rt, double animationId, double nodeTag, jsi::Object config, jsi::Function endCallback) = 0;
  virtual void stopAnimation(jsi::Runtime &rt, double animationId) = 0;
  virtual void setAnimatedNodeValue(jsi::Runtime &rt, double nodeTag, double value) = 0;
  virtual void setAnimatedNodeOffset(jsi::Runtime &rt, double nodeTag, double offset) = 0;
  virtual void flattenAnimatedNodeOffset(jsi::Runtime &rt, double nodeTag) = 0;
  virtual void extractAnimatedNodeOffset(jsi::Runtime &rt, double nodeTag) = 0;
  virtual void connectAnimatedNodeToView(jsi::Runtime &rt, double nodeTag, double viewTag) = 0;
  virtual void disconnectAnimatedNodeFromView(jsi::Runtime &rt, double nodeTag, double viewTag) = 0;
  virtual void restoreDefaultValues(jsi::Runtime &rt, double nodeTag) = 0;
  virtual void dropAnimatedNode(jsi::Runtime &rt, double tag) = 0;
  virtual void addAnimatedEventToView(jsi::Runtime &rt, double viewTag, jsi::String eventName, jsi::Object eventMapping) = 0;
  virtual void removeAnimatedEventFromView(jsi::Runtime &rt, double viewTag, jsi::String eventName, double animatedNodeTag) = 0;
  virtual void addListener(jsi::Runtime &rt, jsi::String eventName) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;
  virtual void queueAndExecuteBatchedOperations(jsi::Runtime &rt, jsi::Array operationsAndArgs) = 0;

};

template <typename T>
class JSI_EXPORT NativeAnimatedModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "NativeAnimatedModule";

protected:
  NativeAnimatedModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeAnimatedModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeAnimatedModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeAnimatedModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void startOperationBatch(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::startOperationBatch) == 1,
          "Expected startOperationBatch(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::startOperationBatch, jsInvoker_, instance_);
    }
    void finishOperationBatch(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::finishOperationBatch) == 1,
          "Expected finishOperationBatch(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::finishOperationBatch, jsInvoker_, instance_);
    }
    void createAnimatedNode(jsi::Runtime &rt, double tag, jsi::Object config) override {
      static_assert(
          bridging::getParameterCount(&T::createAnimatedNode) == 3,
          "Expected createAnimatedNode(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::createAnimatedNode, jsInvoker_, instance_, std::move(tag), std::move(config));
    }
    void updateAnimatedNodeConfig(jsi::Runtime &rt, double tag, jsi::Object config) override {
      static_assert(
          bridging::getParameterCount(&T::updateAnimatedNodeConfig) == 3,
          "Expected updateAnimatedNodeConfig(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::updateAnimatedNodeConfig, jsInvoker_, instance_, std::move(tag), std::move(config));
    }
    void getValue(jsi::Runtime &rt, double tag, jsi::Function saveValueCallback) override {
      static_assert(
          bridging::getParameterCount(&T::getValue) == 3,
          "Expected getValue(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getValue, jsInvoker_, instance_, std::move(tag), std::move(saveValueCallback));
    }
    void startListeningToAnimatedNodeValue(jsi::Runtime &rt, double tag) override {
      static_assert(
          bridging::getParameterCount(&T::startListeningToAnimatedNodeValue) == 2,
          "Expected startListeningToAnimatedNodeValue(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::startListeningToAnimatedNodeValue, jsInvoker_, instance_, std::move(tag));
    }
    void stopListeningToAnimatedNodeValue(jsi::Runtime &rt, double tag) override {
      static_assert(
          bridging::getParameterCount(&T::stopListeningToAnimatedNodeValue) == 2,
          "Expected stopListeningToAnimatedNodeValue(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::stopListeningToAnimatedNodeValue, jsInvoker_, instance_, std::move(tag));
    }
    void connectAnimatedNodes(jsi::Runtime &rt, double parentTag, double childTag) override {
      static_assert(
          bridging::getParameterCount(&T::connectAnimatedNodes) == 3,
          "Expected connectAnimatedNodes(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::connectAnimatedNodes, jsInvoker_, instance_, std::move(parentTag), std::move(childTag));
    }
    void disconnectAnimatedNodes(jsi::Runtime &rt, double parentTag, double childTag) override {
      static_assert(
          bridging::getParameterCount(&T::disconnectAnimatedNodes) == 3,
          "Expected disconnectAnimatedNodes(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::disconnectAnimatedNodes, jsInvoker_, instance_, std::move(parentTag), std::move(childTag));
    }
    void startAnimatingNode(jsi::Runtime &rt, double animationId, double nodeTag, jsi::Object config, jsi::Function endCallback) override {
      static_assert(
          bridging::getParameterCount(&T::startAnimatingNode) == 5,
          "Expected startAnimatingNode(...) to have 5 parameters");

      return bridging::callFromJs<void>(
          rt, &T::startAnimatingNode, jsInvoker_, instance_, std::move(animationId), std::move(nodeTag), std::move(config), std::move(endCallback));
    }
    void stopAnimation(jsi::Runtime &rt, double animationId) override {
      static_assert(
          bridging::getParameterCount(&T::stopAnimation) == 2,
          "Expected stopAnimation(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::stopAnimation, jsInvoker_, instance_, std::move(animationId));
    }
    void setAnimatedNodeValue(jsi::Runtime &rt, double nodeTag, double value) override {
      static_assert(
          bridging::getParameterCount(&T::setAnimatedNodeValue) == 3,
          "Expected setAnimatedNodeValue(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setAnimatedNodeValue, jsInvoker_, instance_, std::move(nodeTag), std::move(value));
    }
    void setAnimatedNodeOffset(jsi::Runtime &rt, double nodeTag, double offset) override {
      static_assert(
          bridging::getParameterCount(&T::setAnimatedNodeOffset) == 3,
          "Expected setAnimatedNodeOffset(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setAnimatedNodeOffset, jsInvoker_, instance_, std::move(nodeTag), std::move(offset));
    }
    void flattenAnimatedNodeOffset(jsi::Runtime &rt, double nodeTag) override {
      static_assert(
          bridging::getParameterCount(&T::flattenAnimatedNodeOffset) == 2,
          "Expected flattenAnimatedNodeOffset(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::flattenAnimatedNodeOffset, jsInvoker_, instance_, std::move(nodeTag));
    }
    void extractAnimatedNodeOffset(jsi::Runtime &rt, double nodeTag) override {
      static_assert(
          bridging::getParameterCount(&T::extractAnimatedNodeOffset) == 2,
          "Expected extractAnimatedNodeOffset(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::extractAnimatedNodeOffset, jsInvoker_, instance_, std::move(nodeTag));
    }
    void connectAnimatedNodeToView(jsi::Runtime &rt, double nodeTag, double viewTag) override {
      static_assert(
          bridging::getParameterCount(&T::connectAnimatedNodeToView) == 3,
          "Expected connectAnimatedNodeToView(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::connectAnimatedNodeToView, jsInvoker_, instance_, std::move(nodeTag), std::move(viewTag));
    }
    void disconnectAnimatedNodeFromView(jsi::Runtime &rt, double nodeTag, double viewTag) override {
      static_assert(
          bridging::getParameterCount(&T::disconnectAnimatedNodeFromView) == 3,
          "Expected disconnectAnimatedNodeFromView(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::disconnectAnimatedNodeFromView, jsInvoker_, instance_, std::move(nodeTag), std::move(viewTag));
    }
    void restoreDefaultValues(jsi::Runtime &rt, double nodeTag) override {
      static_assert(
          bridging::getParameterCount(&T::restoreDefaultValues) == 2,
          "Expected restoreDefaultValues(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::restoreDefaultValues, jsInvoker_, instance_, std::move(nodeTag));
    }
    void dropAnimatedNode(jsi::Runtime &rt, double tag) override {
      static_assert(
          bridging::getParameterCount(&T::dropAnimatedNode) == 2,
          "Expected dropAnimatedNode(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::dropAnimatedNode, jsInvoker_, instance_, std::move(tag));
    }
    void addAnimatedEventToView(jsi::Runtime &rt, double viewTag, jsi::String eventName, jsi::Object eventMapping) override {
      static_assert(
          bridging::getParameterCount(&T::addAnimatedEventToView) == 4,
          "Expected addAnimatedEventToView(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addAnimatedEventToView, jsInvoker_, instance_, std::move(viewTag), std::move(eventName), std::move(eventMapping));
    }
    void removeAnimatedEventFromView(jsi::Runtime &rt, double viewTag, jsi::String eventName, double animatedNodeTag) override {
      static_assert(
          bridging::getParameterCount(&T::removeAnimatedEventFromView) == 4,
          "Expected removeAnimatedEventFromView(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeAnimatedEventFromView, jsInvoker_, instance_, std::move(viewTag), std::move(eventName), std::move(animatedNodeTag));
    }
    void addListener(jsi::Runtime &rt, jsi::String eventName) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventName));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }
    void queueAndExecuteBatchedOperations(jsi::Runtime &rt, jsi::Array operationsAndArgs) override {
      static_assert(
          bridging::getParameterCount(&T::queueAndExecuteBatchedOperations) == 2,
          "Expected queueAndExecuteBatchedOperations(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::queueAndExecuteBatchedOperations, jsInvoker_, instance_, std::move(operationsAndArgs));
    }

  private:
    friend class NativeAnimatedModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeAnimatedTurboModuleEndResult

template <typename P0, typename P1>
struct NativeAnimatedTurboModuleEndResult {
  P0 finished;
  P1 value;
  bool operator==(const NativeAnimatedTurboModuleEndResult &other) const {
    return finished == other.finished && value == other.value;
  }
};

template <typename T>
struct NativeAnimatedTurboModuleEndResultBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.finished)>(rt, value.getProperty(rt, "finished"), jsInvoker),
      bridging::fromJs<decltype(types.value)>(rt, value.getProperty(rt, "value"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static bool finishedToJs(jsi::Runtime &rt, decltype(types.finished) value) {
    return bridging::toJs(rt, value);
  }

  static double valueToJs(jsi::Runtime &rt, decltype(types.value) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "finished", bridging::toJs(rt, value.finished, jsInvoker));
    if (value.value) {
      result.setProperty(rt, "value", bridging::toJs(rt, value.value.value(), jsInvoker));
    }
    return result;
  }
};



#pragma mark - NativeAnimatedTurboModuleEventMapping

template <typename P0, typename P1>
struct NativeAnimatedTurboModuleEventMapping {
  P0 nativeEventPath;
  P1 animatedValueTag;
  bool operator==(const NativeAnimatedTurboModuleEventMapping &other) const {
    return nativeEventPath == other.nativeEventPath && animatedValueTag == other.animatedValueTag;
  }
};

template <typename T>
struct NativeAnimatedTurboModuleEventMappingBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.nativeEventPath)>(rt, value.getProperty(rt, "nativeEventPath"), jsInvoker),
      bridging::fromJs<decltype(types.animatedValueTag)>(rt, value.getProperty(rt, "animatedValueTag"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::Array nativeEventPathToJs(jsi::Runtime &rt, decltype(types.nativeEventPath) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<double> animatedValueTagToJs(jsi::Runtime &rt, decltype(types.animatedValueTag) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "nativeEventPath", bridging::toJs(rt, value.nativeEventPath, jsInvoker));
    result.setProperty(rt, "animatedValueTag", bridging::toJs(rt, value.animatedValueTag, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativeAnimatedTurboModuleCxxSpecJSI : public TurboModule {
protected:
  NativeAnimatedTurboModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void startOperationBatch(jsi::Runtime &rt) = 0;
  virtual void finishOperationBatch(jsi::Runtime &rt) = 0;
  virtual void createAnimatedNode(jsi::Runtime &rt, double tag, jsi::Object config) = 0;
  virtual void updateAnimatedNodeConfig(jsi::Runtime &rt, double tag, jsi::Object config) = 0;
  virtual void getValue(jsi::Runtime &rt, double tag, jsi::Function saveValueCallback) = 0;
  virtual void startListeningToAnimatedNodeValue(jsi::Runtime &rt, double tag) = 0;
  virtual void stopListeningToAnimatedNodeValue(jsi::Runtime &rt, double tag) = 0;
  virtual void connectAnimatedNodes(jsi::Runtime &rt, double parentTag, double childTag) = 0;
  virtual void disconnectAnimatedNodes(jsi::Runtime &rt, double parentTag, double childTag) = 0;
  virtual void startAnimatingNode(jsi::Runtime &rt, double animationId, double nodeTag, jsi::Object config, jsi::Function endCallback) = 0;
  virtual void stopAnimation(jsi::Runtime &rt, double animationId) = 0;
  virtual void setAnimatedNodeValue(jsi::Runtime &rt, double nodeTag, double value) = 0;
  virtual void setAnimatedNodeOffset(jsi::Runtime &rt, double nodeTag, double offset) = 0;
  virtual void flattenAnimatedNodeOffset(jsi::Runtime &rt, double nodeTag) = 0;
  virtual void extractAnimatedNodeOffset(jsi::Runtime &rt, double nodeTag) = 0;
  virtual void connectAnimatedNodeToView(jsi::Runtime &rt, double nodeTag, double viewTag) = 0;
  virtual void disconnectAnimatedNodeFromView(jsi::Runtime &rt, double nodeTag, double viewTag) = 0;
  virtual void restoreDefaultValues(jsi::Runtime &rt, double nodeTag) = 0;
  virtual void dropAnimatedNode(jsi::Runtime &rt, double tag) = 0;
  virtual void addAnimatedEventToView(jsi::Runtime &rt, double viewTag, jsi::String eventName, jsi::Object eventMapping) = 0;
  virtual void removeAnimatedEventFromView(jsi::Runtime &rt, double viewTag, jsi::String eventName, double animatedNodeTag) = 0;
  virtual void addListener(jsi::Runtime &rt, jsi::String eventName) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;
  virtual void queueAndExecuteBatchedOperations(jsi::Runtime &rt, jsi::Array operationsAndArgs) = 0;

};

template <typename T>
class JSI_EXPORT NativeAnimatedTurboModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "NativeAnimatedTurboModule";

protected:
  NativeAnimatedTurboModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeAnimatedTurboModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeAnimatedTurboModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeAnimatedTurboModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void startOperationBatch(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::startOperationBatch) == 1,
          "Expected startOperationBatch(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::startOperationBatch, jsInvoker_, instance_);
    }
    void finishOperationBatch(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::finishOperationBatch) == 1,
          "Expected finishOperationBatch(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::finishOperationBatch, jsInvoker_, instance_);
    }
    void createAnimatedNode(jsi::Runtime &rt, double tag, jsi::Object config) override {
      static_assert(
          bridging::getParameterCount(&T::createAnimatedNode) == 3,
          "Expected createAnimatedNode(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::createAnimatedNode, jsInvoker_, instance_, std::move(tag), std::move(config));
    }
    void updateAnimatedNodeConfig(jsi::Runtime &rt, double tag, jsi::Object config) override {
      static_assert(
          bridging::getParameterCount(&T::updateAnimatedNodeConfig) == 3,
          "Expected updateAnimatedNodeConfig(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::updateAnimatedNodeConfig, jsInvoker_, instance_, std::move(tag), std::move(config));
    }
    void getValue(jsi::Runtime &rt, double tag, jsi::Function saveValueCallback) override {
      static_assert(
          bridging::getParameterCount(&T::getValue) == 3,
          "Expected getValue(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getValue, jsInvoker_, instance_, std::move(tag), std::move(saveValueCallback));
    }
    void startListeningToAnimatedNodeValue(jsi::Runtime &rt, double tag) override {
      static_assert(
          bridging::getParameterCount(&T::startListeningToAnimatedNodeValue) == 2,
          "Expected startListeningToAnimatedNodeValue(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::startListeningToAnimatedNodeValue, jsInvoker_, instance_, std::move(tag));
    }
    void stopListeningToAnimatedNodeValue(jsi::Runtime &rt, double tag) override {
      static_assert(
          bridging::getParameterCount(&T::stopListeningToAnimatedNodeValue) == 2,
          "Expected stopListeningToAnimatedNodeValue(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::stopListeningToAnimatedNodeValue, jsInvoker_, instance_, std::move(tag));
    }
    void connectAnimatedNodes(jsi::Runtime &rt, double parentTag, double childTag) override {
      static_assert(
          bridging::getParameterCount(&T::connectAnimatedNodes) == 3,
          "Expected connectAnimatedNodes(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::connectAnimatedNodes, jsInvoker_, instance_, std::move(parentTag), std::move(childTag));
    }
    void disconnectAnimatedNodes(jsi::Runtime &rt, double parentTag, double childTag) override {
      static_assert(
          bridging::getParameterCount(&T::disconnectAnimatedNodes) == 3,
          "Expected disconnectAnimatedNodes(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::disconnectAnimatedNodes, jsInvoker_, instance_, std::move(parentTag), std::move(childTag));
    }
    void startAnimatingNode(jsi::Runtime &rt, double animationId, double nodeTag, jsi::Object config, jsi::Function endCallback) override {
      static_assert(
          bridging::getParameterCount(&T::startAnimatingNode) == 5,
          "Expected startAnimatingNode(...) to have 5 parameters");

      return bridging::callFromJs<void>(
          rt, &T::startAnimatingNode, jsInvoker_, instance_, std::move(animationId), std::move(nodeTag), std::move(config), std::move(endCallback));
    }
    void stopAnimation(jsi::Runtime &rt, double animationId) override {
      static_assert(
          bridging::getParameterCount(&T::stopAnimation) == 2,
          "Expected stopAnimation(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::stopAnimation, jsInvoker_, instance_, std::move(animationId));
    }
    void setAnimatedNodeValue(jsi::Runtime &rt, double nodeTag, double value) override {
      static_assert(
          bridging::getParameterCount(&T::setAnimatedNodeValue) == 3,
          "Expected setAnimatedNodeValue(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setAnimatedNodeValue, jsInvoker_, instance_, std::move(nodeTag), std::move(value));
    }
    void setAnimatedNodeOffset(jsi::Runtime &rt, double nodeTag, double offset) override {
      static_assert(
          bridging::getParameterCount(&T::setAnimatedNodeOffset) == 3,
          "Expected setAnimatedNodeOffset(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setAnimatedNodeOffset, jsInvoker_, instance_, std::move(nodeTag), std::move(offset));
    }
    void flattenAnimatedNodeOffset(jsi::Runtime &rt, double nodeTag) override {
      static_assert(
          bridging::getParameterCount(&T::flattenAnimatedNodeOffset) == 2,
          "Expected flattenAnimatedNodeOffset(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::flattenAnimatedNodeOffset, jsInvoker_, instance_, std::move(nodeTag));
    }
    void extractAnimatedNodeOffset(jsi::Runtime &rt, double nodeTag) override {
      static_assert(
          bridging::getParameterCount(&T::extractAnimatedNodeOffset) == 2,
          "Expected extractAnimatedNodeOffset(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::extractAnimatedNodeOffset, jsInvoker_, instance_, std::move(nodeTag));
    }
    void connectAnimatedNodeToView(jsi::Runtime &rt, double nodeTag, double viewTag) override {
      static_assert(
          bridging::getParameterCount(&T::connectAnimatedNodeToView) == 3,
          "Expected connectAnimatedNodeToView(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::connectAnimatedNodeToView, jsInvoker_, instance_, std::move(nodeTag), std::move(viewTag));
    }
    void disconnectAnimatedNodeFromView(jsi::Runtime &rt, double nodeTag, double viewTag) override {
      static_assert(
          bridging::getParameterCount(&T::disconnectAnimatedNodeFromView) == 3,
          "Expected disconnectAnimatedNodeFromView(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::disconnectAnimatedNodeFromView, jsInvoker_, instance_, std::move(nodeTag), std::move(viewTag));
    }
    void restoreDefaultValues(jsi::Runtime &rt, double nodeTag) override {
      static_assert(
          bridging::getParameterCount(&T::restoreDefaultValues) == 2,
          "Expected restoreDefaultValues(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::restoreDefaultValues, jsInvoker_, instance_, std::move(nodeTag));
    }
    void dropAnimatedNode(jsi::Runtime &rt, double tag) override {
      static_assert(
          bridging::getParameterCount(&T::dropAnimatedNode) == 2,
          "Expected dropAnimatedNode(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::dropAnimatedNode, jsInvoker_, instance_, std::move(tag));
    }
    void addAnimatedEventToView(jsi::Runtime &rt, double viewTag, jsi::String eventName, jsi::Object eventMapping) override {
      static_assert(
          bridging::getParameterCount(&T::addAnimatedEventToView) == 4,
          "Expected addAnimatedEventToView(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addAnimatedEventToView, jsInvoker_, instance_, std::move(viewTag), std::move(eventName), std::move(eventMapping));
    }
    void removeAnimatedEventFromView(jsi::Runtime &rt, double viewTag, jsi::String eventName, double animatedNodeTag) override {
      static_assert(
          bridging::getParameterCount(&T::removeAnimatedEventFromView) == 4,
          "Expected removeAnimatedEventFromView(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeAnimatedEventFromView, jsInvoker_, instance_, std::move(viewTag), std::move(eventName), std::move(animatedNodeTag));
    }
    void addListener(jsi::Runtime &rt, jsi::String eventName) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventName));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }
    void queueAndExecuteBatchedOperations(jsi::Runtime &rt, jsi::Array operationsAndArgs) override {
      static_assert(
          bridging::getParameterCount(&T::queueAndExecuteBatchedOperations) == 2,
          "Expected queueAndExecuteBatchedOperations(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::queueAndExecuteBatchedOperations, jsInvoker_, instance_, std::move(operationsAndArgs));
    }

  private:
    friend class NativeAnimatedTurboModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeAppearanceCxxSpecJSI : public TurboModule {
protected:
  NativeAppearanceCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual std::optional<jsi::String> getColorScheme(jsi::Runtime &rt) = 0;
  virtual void setColorScheme(jsi::Runtime &rt, jsi::String colorScheme) = 0;
  virtual void addListener(jsi::Runtime &rt, jsi::String eventName) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;

};

template <typename T>
class JSI_EXPORT NativeAppearanceCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "Appearance";

protected:
  NativeAppearanceCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeAppearanceCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeAppearanceCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeAppearanceCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    std::optional<jsi::String> getColorScheme(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getColorScheme) == 1,
          "Expected getColorScheme(...) to have 1 parameters");

      return bridging::callFromJs<std::optional<jsi::String>>(
          rt, &T::getColorScheme, jsInvoker_, instance_);
    }
    void setColorScheme(jsi::Runtime &rt, jsi::String colorScheme) override {
      static_assert(
          bridging::getParameterCount(&T::setColorScheme) == 2,
          "Expected setColorScheme(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setColorScheme, jsInvoker_, instance_, std::move(colorScheme));
    }
    void addListener(jsi::Runtime &rt, jsi::String eventName) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventName));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }

  private:
    friend class NativeAppearanceCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeAppStateAppState

template <typename P0>
struct NativeAppStateAppState {
  P0 app_state;
  bool operator==(const NativeAppStateAppState &other) const {
    return app_state == other.app_state;
  }
};

template <typename T>
struct NativeAppStateAppStateBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.app_state)>(rt, value.getProperty(rt, "app_state"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::String app_stateToJs(jsi::Runtime &rt, decltype(types.app_state) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "app_state", bridging::toJs(rt, value.app_state, jsInvoker));
    return result;
  }
};



#pragma mark - NativeAppStateAppStateConstants

template <typename P0>
struct NativeAppStateAppStateConstants {
  P0 initialAppState;
  bool operator==(const NativeAppStateAppStateConstants &other) const {
    return initialAppState == other.initialAppState;
  }
};

template <typename T>
struct NativeAppStateAppStateConstantsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.initialAppState)>(rt, value.getProperty(rt, "initialAppState"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::String initialAppStateToJs(jsi::Runtime &rt, decltype(types.initialAppState) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "initialAppState", bridging::toJs(rt, value.initialAppState, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativeAppStateCxxSpecJSI : public TurboModule {
protected:
  NativeAppStateCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual void getCurrentAppState(jsi::Runtime &rt, jsi::Function success, jsi::Function error) = 0;
  virtual void addListener(jsi::Runtime &rt, jsi::String eventName) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;

};

template <typename T>
class JSI_EXPORT NativeAppStateCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "AppState";

protected:
  NativeAppStateCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeAppStateCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeAppStateCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeAppStateCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    void getCurrentAppState(jsi::Runtime &rt, jsi::Function success, jsi::Function error) override {
      static_assert(
          bridging::getParameterCount(&T::getCurrentAppState) == 3,
          "Expected getCurrentAppState(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getCurrentAppState, jsInvoker_, instance_, std::move(success), std::move(error));
    }
    void addListener(jsi::Runtime &rt, jsi::String eventName) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventName));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }

  private:
    friend class NativeAppStateCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeBlobModuleConstants

template <typename P0, typename P1>
struct NativeBlobModuleConstants {
  P0 BLOB_URI_SCHEME;
  P1 BLOB_URI_HOST;
  bool operator==(const NativeBlobModuleConstants &other) const {
    return BLOB_URI_SCHEME == other.BLOB_URI_SCHEME && BLOB_URI_HOST == other.BLOB_URI_HOST;
  }
};

template <typename T>
struct NativeBlobModuleConstantsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.BLOB_URI_SCHEME)>(rt, value.getProperty(rt, "BLOB_URI_SCHEME"), jsInvoker),
      bridging::fromJs<decltype(types.BLOB_URI_HOST)>(rt, value.getProperty(rt, "BLOB_URI_HOST"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static std::optional<jsi::String> BLOB_URI_SCHEMEToJs(jsi::Runtime &rt, decltype(types.BLOB_URI_SCHEME) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> BLOB_URI_HOSTToJs(jsi::Runtime &rt, decltype(types.BLOB_URI_HOST) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "BLOB_URI_SCHEME", bridging::toJs(rt, value.BLOB_URI_SCHEME, jsInvoker));
    result.setProperty(rt, "BLOB_URI_HOST", bridging::toJs(rt, value.BLOB_URI_HOST, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativeBlobModuleCxxSpecJSI : public TurboModule {
protected:
  NativeBlobModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual void addNetworkingHandler(jsi::Runtime &rt) = 0;
  virtual void addWebSocketHandler(jsi::Runtime &rt, double id) = 0;
  virtual void removeWebSocketHandler(jsi::Runtime &rt, double id) = 0;
  virtual void sendOverSocket(jsi::Runtime &rt, jsi::Object blob, double socketID) = 0;
  virtual void createFromParts(jsi::Runtime &rt, jsi::Array parts, jsi::String withId) = 0;
  virtual void release(jsi::Runtime &rt, jsi::String blobId) = 0;

};

template <typename T>
class JSI_EXPORT NativeBlobModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "BlobModule";

protected:
  NativeBlobModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeBlobModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeBlobModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeBlobModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    void addNetworkingHandler(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::addNetworkingHandler) == 1,
          "Expected addNetworkingHandler(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addNetworkingHandler, jsInvoker_, instance_);
    }
    void addWebSocketHandler(jsi::Runtime &rt, double id) override {
      static_assert(
          bridging::getParameterCount(&T::addWebSocketHandler) == 2,
          "Expected addWebSocketHandler(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addWebSocketHandler, jsInvoker_, instance_, std::move(id));
    }
    void removeWebSocketHandler(jsi::Runtime &rt, double id) override {
      static_assert(
          bridging::getParameterCount(&T::removeWebSocketHandler) == 2,
          "Expected removeWebSocketHandler(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeWebSocketHandler, jsInvoker_, instance_, std::move(id));
    }
    void sendOverSocket(jsi::Runtime &rt, jsi::Object blob, double socketID) override {
      static_assert(
          bridging::getParameterCount(&T::sendOverSocket) == 3,
          "Expected sendOverSocket(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::sendOverSocket, jsInvoker_, instance_, std::move(blob), std::move(socketID));
    }
    void createFromParts(jsi::Runtime &rt, jsi::Array parts, jsi::String withId) override {
      static_assert(
          bridging::getParameterCount(&T::createFromParts) == 3,
          "Expected createFromParts(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::createFromParts, jsInvoker_, instance_, std::move(parts), std::move(withId));
    }
    void release(jsi::Runtime &rt, jsi::String blobId) override {
      static_assert(
          bridging::getParameterCount(&T::release) == 2,
          "Expected release(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::release, jsInvoker_, instance_, std::move(blobId));
    }

  private:
    friend class NativeBlobModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeBugReportingCxxSpecJSI : public TurboModule {
protected:
  NativeBugReportingCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void startReportAProblemFlow(jsi::Runtime &rt) = 0;
  virtual void setExtraData(jsi::Runtime &rt, jsi::Object extraData, jsi::Object extraFiles) = 0;

};

template <typename T>
class JSI_EXPORT NativeBugReportingCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "BugReporting";

protected:
  NativeBugReportingCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeBugReportingCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeBugReportingCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeBugReportingCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void startReportAProblemFlow(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::startReportAProblemFlow) == 1,
          "Expected startReportAProblemFlow(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::startReportAProblemFlow, jsInvoker_, instance_);
    }
    void setExtraData(jsi::Runtime &rt, jsi::Object extraData, jsi::Object extraFiles) override {
      static_assert(
          bridging::getParameterCount(&T::setExtraData) == 3,
          "Expected setExtraData(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setExtraData, jsInvoker_, instance_, std::move(extraData), std::move(extraFiles));
    }

  private:
    friend class NativeBugReportingCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeClipboardCxxSpecJSI : public TurboModule {
protected:
  NativeClipboardCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual jsi::Value getString(jsi::Runtime &rt) = 0;
  virtual void setString(jsi::Runtime &rt, jsi::String content) = 0;

};

template <typename T>
class JSI_EXPORT NativeClipboardCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "Clipboard";

protected:
  NativeClipboardCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeClipboardCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeClipboardCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeClipboardCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    jsi::Value getString(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getString) == 1,
          "Expected getString(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getString, jsInvoker_, instance_);
    }
    void setString(jsi::Runtime &rt, jsi::String content) override {
      static_assert(
          bridging::getParameterCount(&T::setString) == 2,
          "Expected setString(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setString, jsInvoker_, instance_, std::move(content));
    }

  private:
    friend class NativeClipboardCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeDeviceEventManagerCxxSpecJSI : public TurboModule {
protected:
  NativeDeviceEventManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void invokeDefaultBackPressHandler(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeDeviceEventManagerCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "DeviceEventManager";

protected:
  NativeDeviceEventManagerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeDeviceEventManagerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeDeviceEventManagerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeDeviceEventManagerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void invokeDefaultBackPressHandler(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::invokeDefaultBackPressHandler) == 1,
          "Expected invokeDefaultBackPressHandler(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::invokeDefaultBackPressHandler, jsInvoker_, instance_);
    }

  private:
    friend class NativeDeviceEventManagerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeDeviceInfoDeviceInfoConstants

template <typename P0, typename P1>
struct NativeDeviceInfoDeviceInfoConstants {
  P0 Dimensions;
  P1 isIPhoneX_deprecated;
  bool operator==(const NativeDeviceInfoDeviceInfoConstants &other) const {
    return Dimensions == other.Dimensions && isIPhoneX_deprecated == other.isIPhoneX_deprecated;
  }
};

template <typename T>
struct NativeDeviceInfoDeviceInfoConstantsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.Dimensions)>(rt, value.getProperty(rt, "Dimensions"), jsInvoker),
      bridging::fromJs<decltype(types.isIPhoneX_deprecated)>(rt, value.getProperty(rt, "isIPhoneX_deprecated"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::Object DimensionsToJs(jsi::Runtime &rt, decltype(types.Dimensions) value) {
    return bridging::toJs(rt, value);
  }

  static bool isIPhoneX_deprecatedToJs(jsi::Runtime &rt, decltype(types.isIPhoneX_deprecated) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "Dimensions", bridging::toJs(rt, value.Dimensions, jsInvoker));
    if (value.isIPhoneX_deprecated) {
      result.setProperty(rt, "isIPhoneX_deprecated", bridging::toJs(rt, value.isIPhoneX_deprecated.value(), jsInvoker));
    }
    return result;
  }
};



#pragma mark - NativeDeviceInfoDimensionsPayload

template <typename P0, typename P1, typename P2, typename P3>
struct NativeDeviceInfoDimensionsPayload {
  P0 window;
  P1 screen;
  P2 windowPhysicalPixels;
  P3 screenPhysicalPixels;
  bool operator==(const NativeDeviceInfoDimensionsPayload &other) const {
    return window == other.window && screen == other.screen && windowPhysicalPixels == other.windowPhysicalPixels && screenPhysicalPixels == other.screenPhysicalPixels;
  }
};

template <typename T>
struct NativeDeviceInfoDimensionsPayloadBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.window)>(rt, value.getProperty(rt, "window"), jsInvoker),
      bridging::fromJs<decltype(types.screen)>(rt, value.getProperty(rt, "screen"), jsInvoker),
      bridging::fromJs<decltype(types.windowPhysicalPixels)>(rt, value.getProperty(rt, "windowPhysicalPixels"), jsInvoker),
      bridging::fromJs<decltype(types.screenPhysicalPixels)>(rt, value.getProperty(rt, "screenPhysicalPixels"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::Object windowToJs(jsi::Runtime &rt, decltype(types.window) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Object screenToJs(jsi::Runtime &rt, decltype(types.screen) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Object windowPhysicalPixelsToJs(jsi::Runtime &rt, decltype(types.windowPhysicalPixels) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Object screenPhysicalPixelsToJs(jsi::Runtime &rt, decltype(types.screenPhysicalPixels) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    if (value.window) {
      result.setProperty(rt, "window", bridging::toJs(rt, value.window.value(), jsInvoker));
    }
    if (value.screen) {
      result.setProperty(rt, "screen", bridging::toJs(rt, value.screen.value(), jsInvoker));
    }
    if (value.windowPhysicalPixels) {
      result.setProperty(rt, "windowPhysicalPixels", bridging::toJs(rt, value.windowPhysicalPixels.value(), jsInvoker));
    }
    if (value.screenPhysicalPixels) {
      result.setProperty(rt, "screenPhysicalPixels", bridging::toJs(rt, value.screenPhysicalPixels.value(), jsInvoker));
    }
    return result;
  }
};



#pragma mark - NativeDeviceInfoDisplayMetrics

template <typename P0, typename P1, typename P2, typename P3>
struct NativeDeviceInfoDisplayMetrics {
  P0 width;
  P1 height;
  P2 scale;
  P3 fontScale;
  bool operator==(const NativeDeviceInfoDisplayMetrics &other) const {
    return width == other.width && height == other.height && scale == other.scale && fontScale == other.fontScale;
  }
};

template <typename T>
struct NativeDeviceInfoDisplayMetricsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.width)>(rt, value.getProperty(rt, "width"), jsInvoker),
      bridging::fromJs<decltype(types.height)>(rt, value.getProperty(rt, "height"), jsInvoker),
      bridging::fromJs<decltype(types.scale)>(rt, value.getProperty(rt, "scale"), jsInvoker),
      bridging::fromJs<decltype(types.fontScale)>(rt, value.getProperty(rt, "fontScale"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static double widthToJs(jsi::Runtime &rt, decltype(types.width) value) {
    return bridging::toJs(rt, value);
  }

  static double heightToJs(jsi::Runtime &rt, decltype(types.height) value) {
    return bridging::toJs(rt, value);
  }

  static double scaleToJs(jsi::Runtime &rt, decltype(types.scale) value) {
    return bridging::toJs(rt, value);
  }

  static double fontScaleToJs(jsi::Runtime &rt, decltype(types.fontScale) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "width", bridging::toJs(rt, value.width, jsInvoker));
    result.setProperty(rt, "height", bridging::toJs(rt, value.height, jsInvoker));
    result.setProperty(rt, "scale", bridging::toJs(rt, value.scale, jsInvoker));
    result.setProperty(rt, "fontScale", bridging::toJs(rt, value.fontScale, jsInvoker));
    return result;
  }
};



#pragma mark - NativeDeviceInfoDisplayMetricsAndroid

template <typename P0, typename P1, typename P2, typename P3, typename P4>
struct NativeDeviceInfoDisplayMetricsAndroid {
  P0 width;
  P1 height;
  P2 scale;
  P3 fontScale;
  P4 densityDpi;
  bool operator==(const NativeDeviceInfoDisplayMetricsAndroid &other) const {
    return width == other.width && height == other.height && scale == other.scale && fontScale == other.fontScale && densityDpi == other.densityDpi;
  }
};

template <typename T>
struct NativeDeviceInfoDisplayMetricsAndroidBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.width)>(rt, value.getProperty(rt, "width"), jsInvoker),
      bridging::fromJs<decltype(types.height)>(rt, value.getProperty(rt, "height"), jsInvoker),
      bridging::fromJs<decltype(types.scale)>(rt, value.getProperty(rt, "scale"), jsInvoker),
      bridging::fromJs<decltype(types.fontScale)>(rt, value.getProperty(rt, "fontScale"), jsInvoker),
      bridging::fromJs<decltype(types.densityDpi)>(rt, value.getProperty(rt, "densityDpi"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static double widthToJs(jsi::Runtime &rt, decltype(types.width) value) {
    return bridging::toJs(rt, value);
  }

  static double heightToJs(jsi::Runtime &rt, decltype(types.height) value) {
    return bridging::toJs(rt, value);
  }

  static double scaleToJs(jsi::Runtime &rt, decltype(types.scale) value) {
    return bridging::toJs(rt, value);
  }

  static double fontScaleToJs(jsi::Runtime &rt, decltype(types.fontScale) value) {
    return bridging::toJs(rt, value);
  }

  static double densityDpiToJs(jsi::Runtime &rt, decltype(types.densityDpi) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "width", bridging::toJs(rt, value.width, jsInvoker));
    result.setProperty(rt, "height", bridging::toJs(rt, value.height, jsInvoker));
    result.setProperty(rt, "scale", bridging::toJs(rt, value.scale, jsInvoker));
    result.setProperty(rt, "fontScale", bridging::toJs(rt, value.fontScale, jsInvoker));
    result.setProperty(rt, "densityDpi", bridging::toJs(rt, value.densityDpi, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativeDeviceInfoCxxSpecJSI : public TurboModule {
protected:
  NativeDeviceInfoCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeDeviceInfoCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "DeviceInfo";

protected:
  NativeDeviceInfoCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeDeviceInfoCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeDeviceInfoCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeDeviceInfoCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }

  private:
    friend class NativeDeviceInfoCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeDevLoadingViewCxxSpecJSI : public TurboModule {
protected:
  NativeDevLoadingViewCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void showMessage(jsi::Runtime &rt, jsi::String message, std::optional<double> withColor, std::optional<double> withBackgroundColor) = 0;
  virtual void hide(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeDevLoadingViewCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "DevLoadingView";

protected:
  NativeDevLoadingViewCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeDevLoadingViewCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeDevLoadingViewCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeDevLoadingViewCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void showMessage(jsi::Runtime &rt, jsi::String message, std::optional<double> withColor, std::optional<double> withBackgroundColor) override {
      static_assert(
          bridging::getParameterCount(&T::showMessage) == 4,
          "Expected showMessage(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::showMessage, jsInvoker_, instance_, std::move(message), std::move(withColor), std::move(withBackgroundColor));
    }
    void hide(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::hide) == 1,
          "Expected hide(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::hide, jsInvoker_, instance_);
    }

  private:
    friend class NativeDevLoadingViewCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeDevMenuCxxSpecJSI : public TurboModule {
protected:
  NativeDevMenuCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void show(jsi::Runtime &rt) = 0;
  virtual void reload(jsi::Runtime &rt) = 0;
  virtual void setProfilingEnabled(jsi::Runtime &rt, bool enabled) = 0;
  virtual void setHotLoadingEnabled(jsi::Runtime &rt, bool enabled) = 0;

};

template <typename T>
class JSI_EXPORT NativeDevMenuCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "DevMenu";

protected:
  NativeDevMenuCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeDevMenuCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeDevMenuCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeDevMenuCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void show(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::show) == 1,
          "Expected show(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::show, jsInvoker_, instance_);
    }
    void reload(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::reload) == 1,
          "Expected reload(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::reload, jsInvoker_, instance_);
    }
    void setProfilingEnabled(jsi::Runtime &rt, bool enabled) override {
      static_assert(
          bridging::getParameterCount(&T::setProfilingEnabled) == 2,
          "Expected setProfilingEnabled(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setProfilingEnabled, jsInvoker_, instance_, std::move(enabled));
    }
    void setHotLoadingEnabled(jsi::Runtime &rt, bool enabled) override {
      static_assert(
          bridging::getParameterCount(&T::setHotLoadingEnabled) == 2,
          "Expected setHotLoadingEnabled(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setHotLoadingEnabled, jsInvoker_, instance_, std::move(enabled));
    }

  private:
    friend class NativeDevMenuCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeDevSettingsCxxSpecJSI : public TurboModule {
protected:
  NativeDevSettingsCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void reload(jsi::Runtime &rt) = 0;
  virtual void reloadWithReason(jsi::Runtime &rt, jsi::String reason) = 0;
  virtual void onFastRefresh(jsi::Runtime &rt) = 0;
  virtual void setHotLoadingEnabled(jsi::Runtime &rt, bool isHotLoadingEnabled) = 0;
  virtual void setProfilingEnabled(jsi::Runtime &rt, bool isProfilingEnabled) = 0;
  virtual void toggleElementInspector(jsi::Runtime &rt) = 0;
  virtual void addMenuItem(jsi::Runtime &rt, jsi::String title) = 0;
  virtual void openDebugger(jsi::Runtime &rt) = 0;
  virtual void addListener(jsi::Runtime &rt, jsi::String eventName) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;
  virtual void setIsShakeToShowDevMenuEnabled(jsi::Runtime &rt, bool enabled) = 0;

};

template <typename T>
class JSI_EXPORT NativeDevSettingsCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "DevSettings";

protected:
  NativeDevSettingsCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeDevSettingsCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeDevSettingsCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeDevSettingsCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void reload(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::reload) == 1,
          "Expected reload(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::reload, jsInvoker_, instance_);
    }
    void reloadWithReason(jsi::Runtime &rt, jsi::String reason) override {
      static_assert(
          bridging::getParameterCount(&T::reloadWithReason) == 2,
          "Expected reloadWithReason(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::reloadWithReason, jsInvoker_, instance_, std::move(reason));
    }
    void onFastRefresh(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::onFastRefresh) == 1,
          "Expected onFastRefresh(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::onFastRefresh, jsInvoker_, instance_);
    }
    void setHotLoadingEnabled(jsi::Runtime &rt, bool isHotLoadingEnabled) override {
      static_assert(
          bridging::getParameterCount(&T::setHotLoadingEnabled) == 2,
          "Expected setHotLoadingEnabled(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setHotLoadingEnabled, jsInvoker_, instance_, std::move(isHotLoadingEnabled));
    }
    void setProfilingEnabled(jsi::Runtime &rt, bool isProfilingEnabled) override {
      static_assert(
          bridging::getParameterCount(&T::setProfilingEnabled) == 2,
          "Expected setProfilingEnabled(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setProfilingEnabled, jsInvoker_, instance_, std::move(isProfilingEnabled));
    }
    void toggleElementInspector(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::toggleElementInspector) == 1,
          "Expected toggleElementInspector(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::toggleElementInspector, jsInvoker_, instance_);
    }
    void addMenuItem(jsi::Runtime &rt, jsi::String title) override {
      static_assert(
          bridging::getParameterCount(&T::addMenuItem) == 2,
          "Expected addMenuItem(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addMenuItem, jsInvoker_, instance_, std::move(title));
    }
    void openDebugger(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::openDebugger) == 1,
          "Expected openDebugger(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::openDebugger, jsInvoker_, instance_);
    }
    void addListener(jsi::Runtime &rt, jsi::String eventName) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventName));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }
    void setIsShakeToShowDevMenuEnabled(jsi::Runtime &rt, bool enabled) override {
      static_assert(
          bridging::getParameterCount(&T::setIsShakeToShowDevMenuEnabled) == 2,
          "Expected setIsShakeToShowDevMenuEnabled(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setIsShakeToShowDevMenuEnabled, jsInvoker_, instance_, std::move(enabled));
    }

  private:
    friend class NativeDevSettingsCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeDialogManagerAndroidDialogOptions

template <typename P0, typename P1, typename P2, typename P3, typename P4, typename P5, typename P6>
struct NativeDialogManagerAndroidDialogOptions {
  P0 title;
  P1 message;
  P2 buttonPositive;
  P3 buttonNegative;
  P4 buttonNeutral;
  P5 items;
  P6 cancelable;
  bool operator==(const NativeDialogManagerAndroidDialogOptions &other) const {
    return title == other.title && message == other.message && buttonPositive == other.buttonPositive && buttonNegative == other.buttonNegative && buttonNeutral == other.buttonNeutral && items == other.items && cancelable == other.cancelable;
  }
};

template <typename T>
struct NativeDialogManagerAndroidDialogOptionsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.title)>(rt, value.getProperty(rt, "title"), jsInvoker),
      bridging::fromJs<decltype(types.message)>(rt, value.getProperty(rt, "message"), jsInvoker),
      bridging::fromJs<decltype(types.buttonPositive)>(rt, value.getProperty(rt, "buttonPositive"), jsInvoker),
      bridging::fromJs<decltype(types.buttonNegative)>(rt, value.getProperty(rt, "buttonNegative"), jsInvoker),
      bridging::fromJs<decltype(types.buttonNeutral)>(rt, value.getProperty(rt, "buttonNeutral"), jsInvoker),
      bridging::fromJs<decltype(types.items)>(rt, value.getProperty(rt, "items"), jsInvoker),
      bridging::fromJs<decltype(types.cancelable)>(rt, value.getProperty(rt, "cancelable"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::String titleToJs(jsi::Runtime &rt, decltype(types.title) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String messageToJs(jsi::Runtime &rt, decltype(types.message) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String buttonPositiveToJs(jsi::Runtime &rt, decltype(types.buttonPositive) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String buttonNegativeToJs(jsi::Runtime &rt, decltype(types.buttonNegative) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String buttonNeutralToJs(jsi::Runtime &rt, decltype(types.buttonNeutral) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Array itemsToJs(jsi::Runtime &rt, decltype(types.items) value) {
    return bridging::toJs(rt, value);
  }

  static bool cancelableToJs(jsi::Runtime &rt, decltype(types.cancelable) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    if (value.title) {
      result.setProperty(rt, "title", bridging::toJs(rt, value.title.value(), jsInvoker));
    }
    if (value.message) {
      result.setProperty(rt, "message", bridging::toJs(rt, value.message.value(), jsInvoker));
    }
    if (value.buttonPositive) {
      result.setProperty(rt, "buttonPositive", bridging::toJs(rt, value.buttonPositive.value(), jsInvoker));
    }
    if (value.buttonNegative) {
      result.setProperty(rt, "buttonNegative", bridging::toJs(rt, value.buttonNegative.value(), jsInvoker));
    }
    if (value.buttonNeutral) {
      result.setProperty(rt, "buttonNeutral", bridging::toJs(rt, value.buttonNeutral.value(), jsInvoker));
    }
    if (value.items) {
      result.setProperty(rt, "items", bridging::toJs(rt, value.items.value(), jsInvoker));
    }
    if (value.cancelable) {
      result.setProperty(rt, "cancelable", bridging::toJs(rt, value.cancelable.value(), jsInvoker));
    }
    return result;
  }
};

class JSI_EXPORT NativeDialogManagerAndroidCxxSpecJSI : public TurboModule {
protected:
  NativeDialogManagerAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual void showAlert(jsi::Runtime &rt, jsi::Object config, jsi::Function onError, jsi::Function onAction) = 0;

};

template <typename T>
class JSI_EXPORT NativeDialogManagerAndroidCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "DialogManagerAndroid";

protected:
  NativeDialogManagerAndroidCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeDialogManagerAndroidCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeDialogManagerAndroidCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeDialogManagerAndroidCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    void showAlert(jsi::Runtime &rt, jsi::Object config, jsi::Function onError, jsi::Function onAction) override {
      static_assert(
          bridging::getParameterCount(&T::showAlert) == 4,
          "Expected showAlert(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::showAlert, jsInvoker_, instance_, std::move(config), std::move(onError), std::move(onAction));
    }

  private:
    friend class NativeDialogManagerAndroidCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeExceptionsManagerExceptionData

template <typename P0, typename P1, typename P2, typename P3, typename P4, typename P5, typename P6, typename P7>
struct NativeExceptionsManagerExceptionData {
  P0 message;
  P1 originalMessage;
  P2 name;
  P3 componentStack;
  P4 stack;
  P5 id;
  P6 isFatal;
  P7 extraData;
  bool operator==(const NativeExceptionsManagerExceptionData &other) const {
    return message == other.message && originalMessage == other.originalMessage && name == other.name && componentStack == other.componentStack && stack == other.stack && id == other.id && isFatal == other.isFatal && extraData == other.extraData;
  }
};

template <typename T>
struct NativeExceptionsManagerExceptionDataBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.message)>(rt, value.getProperty(rt, "message"), jsInvoker),
      bridging::fromJs<decltype(types.originalMessage)>(rt, value.getProperty(rt, "originalMessage"), jsInvoker),
      bridging::fromJs<decltype(types.name)>(rt, value.getProperty(rt, "name"), jsInvoker),
      bridging::fromJs<decltype(types.componentStack)>(rt, value.getProperty(rt, "componentStack"), jsInvoker),
      bridging::fromJs<decltype(types.stack)>(rt, value.getProperty(rt, "stack"), jsInvoker),
      bridging::fromJs<decltype(types.id)>(rt, value.getProperty(rt, "id"), jsInvoker),
      bridging::fromJs<decltype(types.isFatal)>(rt, value.getProperty(rt, "isFatal"), jsInvoker),
      bridging::fromJs<decltype(types.extraData)>(rt, value.getProperty(rt, "extraData"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::String messageToJs(jsi::Runtime &rt, decltype(types.message) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> originalMessageToJs(jsi::Runtime &rt, decltype(types.originalMessage) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> nameToJs(jsi::Runtime &rt, decltype(types.name) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> componentStackToJs(jsi::Runtime &rt, decltype(types.componentStack) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Array stackToJs(jsi::Runtime &rt, decltype(types.stack) value) {
    return bridging::toJs(rt, value);
  }

  static double idToJs(jsi::Runtime &rt, decltype(types.id) value) {
    return bridging::toJs(rt, value);
  }

  static bool isFatalToJs(jsi::Runtime &rt, decltype(types.isFatal) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Object extraDataToJs(jsi::Runtime &rt, decltype(types.extraData) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "message", bridging::toJs(rt, value.message, jsInvoker));
    result.setProperty(rt, "originalMessage", bridging::toJs(rt, value.originalMessage, jsInvoker));
    result.setProperty(rt, "name", bridging::toJs(rt, value.name, jsInvoker));
    result.setProperty(rt, "componentStack", bridging::toJs(rt, value.componentStack, jsInvoker));
    result.setProperty(rt, "stack", bridging::toJs(rt, value.stack, jsInvoker));
    result.setProperty(rt, "id", bridging::toJs(rt, value.id, jsInvoker));
    result.setProperty(rt, "isFatal", bridging::toJs(rt, value.isFatal, jsInvoker));
    if (value.extraData) {
      result.setProperty(rt, "extraData", bridging::toJs(rt, value.extraData.value(), jsInvoker));
    }
    return result;
  }
};



#pragma mark - NativeExceptionsManagerStackFrame

template <typename P0, typename P1, typename P2, typename P3, typename P4>
struct NativeExceptionsManagerStackFrame {
  P0 column;
  P1 file;
  P2 lineNumber;
  P3 methodName;
  P4 collapse;
  bool operator==(const NativeExceptionsManagerStackFrame &other) const {
    return column == other.column && file == other.file && lineNumber == other.lineNumber && methodName == other.methodName && collapse == other.collapse;
  }
};

template <typename T>
struct NativeExceptionsManagerStackFrameBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.column)>(rt, value.getProperty(rt, "column"), jsInvoker),
      bridging::fromJs<decltype(types.file)>(rt, value.getProperty(rt, "file"), jsInvoker),
      bridging::fromJs<decltype(types.lineNumber)>(rt, value.getProperty(rt, "lineNumber"), jsInvoker),
      bridging::fromJs<decltype(types.methodName)>(rt, value.getProperty(rt, "methodName"), jsInvoker),
      bridging::fromJs<decltype(types.collapse)>(rt, value.getProperty(rt, "collapse"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static std::optional<double> columnToJs(jsi::Runtime &rt, decltype(types.column) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> fileToJs(jsi::Runtime &rt, decltype(types.file) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<double> lineNumberToJs(jsi::Runtime &rt, decltype(types.lineNumber) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String methodNameToJs(jsi::Runtime &rt, decltype(types.methodName) value) {
    return bridging::toJs(rt, value);
  }

  static bool collapseToJs(jsi::Runtime &rt, decltype(types.collapse) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "column", bridging::toJs(rt, value.column, jsInvoker));
    result.setProperty(rt, "file", bridging::toJs(rt, value.file, jsInvoker));
    result.setProperty(rt, "lineNumber", bridging::toJs(rt, value.lineNumber, jsInvoker));
    result.setProperty(rt, "methodName", bridging::toJs(rt, value.methodName, jsInvoker));
    if (value.collapse) {
      result.setProperty(rt, "collapse", bridging::toJs(rt, value.collapse.value(), jsInvoker));
    }
    return result;
  }
};

class JSI_EXPORT NativeExceptionsManagerCxxSpecJSI : public TurboModule {
protected:
  NativeExceptionsManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void reportFatalException(jsi::Runtime &rt, jsi::String message, jsi::Array stack, double exceptionId) = 0;
  virtual void reportSoftException(jsi::Runtime &rt, jsi::String message, jsi::Array stack, double exceptionId) = 0;
  virtual void reportException(jsi::Runtime &rt, jsi::Object data) = 0;
  virtual void dismissRedbox(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeExceptionsManagerCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "ExceptionsManager";

protected:
  NativeExceptionsManagerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeExceptionsManagerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeExceptionsManagerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeExceptionsManagerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void reportFatalException(jsi::Runtime &rt, jsi::String message, jsi::Array stack, double exceptionId) override {
      static_assert(
          bridging::getParameterCount(&T::reportFatalException) == 4,
          "Expected reportFatalException(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::reportFatalException, jsInvoker_, instance_, std::move(message), std::move(stack), std::move(exceptionId));
    }
    void reportSoftException(jsi::Runtime &rt, jsi::String message, jsi::Array stack, double exceptionId) override {
      static_assert(
          bridging::getParameterCount(&T::reportSoftException) == 4,
          "Expected reportSoftException(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::reportSoftException, jsInvoker_, instance_, std::move(message), std::move(stack), std::move(exceptionId));
    }
    void reportException(jsi::Runtime &rt, jsi::Object data) override {
      static_assert(
          bridging::getParameterCount(&T::reportException) == 2,
          "Expected reportException(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::reportException, jsInvoker_, instance_, std::move(data));
    }
    void dismissRedbox(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::dismissRedbox) == 1,
          "Expected dismissRedbox(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::dismissRedbox, jsInvoker_, instance_);
    }

  private:
    friend class NativeExceptionsManagerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeFileReaderModuleCxxSpecJSI : public TurboModule {
protected:
  NativeFileReaderModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Value readAsDataURL(jsi::Runtime &rt, jsi::Object data) = 0;
  virtual jsi::Value readAsText(jsi::Runtime &rt, jsi::Object data, jsi::String encoding) = 0;

};

template <typename T>
class JSI_EXPORT NativeFileReaderModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "FileReaderModule";

protected:
  NativeFileReaderModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeFileReaderModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeFileReaderModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeFileReaderModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Value readAsDataURL(jsi::Runtime &rt, jsi::Object data) override {
      static_assert(
          bridging::getParameterCount(&T::readAsDataURL) == 2,
          "Expected readAsDataURL(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::readAsDataURL, jsInvoker_, instance_, std::move(data));
    }
    jsi::Value readAsText(jsi::Runtime &rt, jsi::Object data, jsi::String encoding) override {
      static_assert(
          bridging::getParameterCount(&T::readAsText) == 3,
          "Expected readAsText(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::readAsText, jsInvoker_, instance_, std::move(data), std::move(encoding));
    }

  private:
    friend class NativeFileReaderModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeFrameRateLoggerCxxSpecJSI : public TurboModule {
protected:
  NativeFrameRateLoggerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void setGlobalOptions(jsi::Runtime &rt, jsi::Object options) = 0;
  virtual void setContext(jsi::Runtime &rt, jsi::String context) = 0;
  virtual void beginScroll(jsi::Runtime &rt) = 0;
  virtual void endScroll(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeFrameRateLoggerCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "FrameRateLogger";

protected:
  NativeFrameRateLoggerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeFrameRateLoggerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeFrameRateLoggerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeFrameRateLoggerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void setGlobalOptions(jsi::Runtime &rt, jsi::Object options) override {
      static_assert(
          bridging::getParameterCount(&T::setGlobalOptions) == 2,
          "Expected setGlobalOptions(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setGlobalOptions, jsInvoker_, instance_, std::move(options));
    }
    void setContext(jsi::Runtime &rt, jsi::String context) override {
      static_assert(
          bridging::getParameterCount(&T::setContext) == 2,
          "Expected setContext(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setContext, jsInvoker_, instance_, std::move(context));
    }
    void beginScroll(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::beginScroll) == 1,
          "Expected beginScroll(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::beginScroll, jsInvoker_, instance_);
    }
    void endScroll(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::endScroll) == 1,
          "Expected endScroll(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::endScroll, jsInvoker_, instance_);
    }

  private:
    friend class NativeFrameRateLoggerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeHeadlessJsTaskSupportCxxSpecJSI : public TurboModule {
protected:
  NativeHeadlessJsTaskSupportCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void notifyTaskFinished(jsi::Runtime &rt, double taskId) = 0;
  virtual jsi::Value notifyTaskRetry(jsi::Runtime &rt, double taskId) = 0;

};

template <typename T>
class JSI_EXPORT NativeHeadlessJsTaskSupportCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "HeadlessJsTaskSupport";

protected:
  NativeHeadlessJsTaskSupportCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeHeadlessJsTaskSupportCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeHeadlessJsTaskSupportCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeHeadlessJsTaskSupportCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void notifyTaskFinished(jsi::Runtime &rt, double taskId) override {
      static_assert(
          bridging::getParameterCount(&T::notifyTaskFinished) == 2,
          "Expected notifyTaskFinished(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::notifyTaskFinished, jsInvoker_, instance_, std::move(taskId));
    }
    jsi::Value notifyTaskRetry(jsi::Runtime &rt, double taskId) override {
      static_assert(
          bridging::getParameterCount(&T::notifyTaskRetry) == 2,
          "Expected notifyTaskRetry(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::notifyTaskRetry, jsInvoker_, instance_, std::move(taskId));
    }

  private:
    friend class NativeHeadlessJsTaskSupportCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeI18nManagerI18nManagerConstants

template <typename P0, typename P1, typename P2>
struct NativeI18nManagerI18nManagerConstants {
  P0 doLeftAndRightSwapInRTL;
  P1 isRTL;
  P2 localeIdentifier;
  bool operator==(const NativeI18nManagerI18nManagerConstants &other) const {
    return doLeftAndRightSwapInRTL == other.doLeftAndRightSwapInRTL && isRTL == other.isRTL && localeIdentifier == other.localeIdentifier;
  }
};

template <typename T>
struct NativeI18nManagerI18nManagerConstantsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.doLeftAndRightSwapInRTL)>(rt, value.getProperty(rt, "doLeftAndRightSwapInRTL"), jsInvoker),
      bridging::fromJs<decltype(types.isRTL)>(rt, value.getProperty(rt, "isRTL"), jsInvoker),
      bridging::fromJs<decltype(types.localeIdentifier)>(rt, value.getProperty(rt, "localeIdentifier"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static bool doLeftAndRightSwapInRTLToJs(jsi::Runtime &rt, decltype(types.doLeftAndRightSwapInRTL) value) {
    return bridging::toJs(rt, value);
  }

  static bool isRTLToJs(jsi::Runtime &rt, decltype(types.isRTL) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> localeIdentifierToJs(jsi::Runtime &rt, decltype(types.localeIdentifier) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "doLeftAndRightSwapInRTL", bridging::toJs(rt, value.doLeftAndRightSwapInRTL, jsInvoker));
    result.setProperty(rt, "isRTL", bridging::toJs(rt, value.isRTL, jsInvoker));
    if (value.localeIdentifier) {
      result.setProperty(rt, "localeIdentifier", bridging::toJs(rt, value.localeIdentifier.value(), jsInvoker));
    }
    return result;
  }
};

class JSI_EXPORT NativeI18nManagerCxxSpecJSI : public TurboModule {
protected:
  NativeI18nManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual void allowRTL(jsi::Runtime &rt, bool allowRTL) = 0;
  virtual void forceRTL(jsi::Runtime &rt, bool forceRTL) = 0;
  virtual void swapLeftAndRightInRTL(jsi::Runtime &rt, bool flipStyles) = 0;

};

template <typename T>
class JSI_EXPORT NativeI18nManagerCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "I18nManager";

protected:
  NativeI18nManagerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeI18nManagerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeI18nManagerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeI18nManagerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    void allowRTL(jsi::Runtime &rt, bool allowRTL) override {
      static_assert(
          bridging::getParameterCount(&T::allowRTL) == 2,
          "Expected allowRTL(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::allowRTL, jsInvoker_, instance_, std::move(allowRTL));
    }
    void forceRTL(jsi::Runtime &rt, bool forceRTL) override {
      static_assert(
          bridging::getParameterCount(&T::forceRTL) == 2,
          "Expected forceRTL(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::forceRTL, jsInvoker_, instance_, std::move(forceRTL));
    }
    void swapLeftAndRightInRTL(jsi::Runtime &rt, bool flipStyles) override {
      static_assert(
          bridging::getParameterCount(&T::swapLeftAndRightInRTL) == 2,
          "Expected swapLeftAndRightInRTL(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::swapLeftAndRightInRTL, jsInvoker_, instance_, std::move(flipStyles));
    }

  private:
    friend class NativeI18nManagerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeImageEditorOptions

template <typename P0, typename P1, typename P2, typename P3, typename P4>
struct NativeImageEditorOptions {
  P0 offset;
  P1 size;
  P2 displaySize;
  P3 resizeMode;
  P4 allowExternalStorage;
  bool operator==(const NativeImageEditorOptions &other) const {
    return offset == other.offset && size == other.size && displaySize == other.displaySize && resizeMode == other.resizeMode && allowExternalStorage == other.allowExternalStorage;
  }
};

template <typename T>
struct NativeImageEditorOptionsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.offset)>(rt, value.getProperty(rt, "offset"), jsInvoker),
      bridging::fromJs<decltype(types.size)>(rt, value.getProperty(rt, "size"), jsInvoker),
      bridging::fromJs<decltype(types.displaySize)>(rt, value.getProperty(rt, "displaySize"), jsInvoker),
      bridging::fromJs<decltype(types.resizeMode)>(rt, value.getProperty(rt, "resizeMode"), jsInvoker),
      bridging::fromJs<decltype(types.allowExternalStorage)>(rt, value.getProperty(rt, "allowExternalStorage"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::Object offsetToJs(jsi::Runtime &rt, decltype(types.offset) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Object sizeToJs(jsi::Runtime &rt, decltype(types.size) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::Object> displaySizeToJs(jsi::Runtime &rt, decltype(types.displaySize) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> resizeModeToJs(jsi::Runtime &rt, decltype(types.resizeMode) value) {
    return bridging::toJs(rt, value);
  }

  static bool allowExternalStorageToJs(jsi::Runtime &rt, decltype(types.allowExternalStorage) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "offset", bridging::toJs(rt, value.offset, jsInvoker));
    result.setProperty(rt, "size", bridging::toJs(rt, value.size, jsInvoker));
    if (value.displaySize) {
      result.setProperty(rt, "displaySize", bridging::toJs(rt, value.displaySize.value(), jsInvoker));
    }
    if (value.resizeMode) {
      result.setProperty(rt, "resizeMode", bridging::toJs(rt, value.resizeMode.value(), jsInvoker));
    }
    if (value.allowExternalStorage) {
      result.setProperty(rt, "allowExternalStorage", bridging::toJs(rt, value.allowExternalStorage.value(), jsInvoker));
    }
    return result;
  }
};

class JSI_EXPORT NativeImageEditorCxxSpecJSI : public TurboModule {
protected:
  NativeImageEditorCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual void cropImage(jsi::Runtime &rt, jsi::String uri, jsi::Object cropData, jsi::Function successCallback, jsi::Function errorCallback) = 0;

};

template <typename T>
class JSI_EXPORT NativeImageEditorCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "ImageEditingManager";

protected:
  NativeImageEditorCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeImageEditorCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeImageEditorCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeImageEditorCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    void cropImage(jsi::Runtime &rt, jsi::String uri, jsi::Object cropData, jsi::Function successCallback, jsi::Function errorCallback) override {
      static_assert(
          bridging::getParameterCount(&T::cropImage) == 5,
          "Expected cropImage(...) to have 5 parameters");

      return bridging::callFromJs<void>(
          rt, &T::cropImage, jsInvoker_, instance_, std::move(uri), std::move(cropData), std::move(successCallback), std::move(errorCallback));
    }

  private:
    friend class NativeImageEditorCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeImageLoaderAndroidImageSize

template <typename P0, typename P1>
struct NativeImageLoaderAndroidImageSize {
  P0 width;
  P1 height;
  bool operator==(const NativeImageLoaderAndroidImageSize &other) const {
    return width == other.width && height == other.height;
  }
};

template <typename T>
struct NativeImageLoaderAndroidImageSizeBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.width)>(rt, value.getProperty(rt, "width"), jsInvoker),
      bridging::fromJs<decltype(types.height)>(rt, value.getProperty(rt, "height"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static double widthToJs(jsi::Runtime &rt, decltype(types.width) value) {
    return bridging::toJs(rt, value);
  }

  static double heightToJs(jsi::Runtime &rt, decltype(types.height) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "width", bridging::toJs(rt, value.width, jsInvoker));
    result.setProperty(rt, "height", bridging::toJs(rt, value.height, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativeImageLoaderAndroidCxxSpecJSI : public TurboModule {
protected:
  NativeImageLoaderAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void abortRequest(jsi::Runtime &rt, double requestId) = 0;
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual jsi::Value getSize(jsi::Runtime &rt, jsi::String uri) = 0;
  virtual jsi::Value getSizeWithHeaders(jsi::Runtime &rt, jsi::String uri, jsi::Object headers) = 0;
  virtual jsi::Value prefetchImage(jsi::Runtime &rt, jsi::String uri, double requestId) = 0;
  virtual jsi::Value queryCache(jsi::Runtime &rt, jsi::Array uris) = 0;

};

template <typename T>
class JSI_EXPORT NativeImageLoaderAndroidCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "ImageLoader";

protected:
  NativeImageLoaderAndroidCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeImageLoaderAndroidCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeImageLoaderAndroidCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeImageLoaderAndroidCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void abortRequest(jsi::Runtime &rt, double requestId) override {
      static_assert(
          bridging::getParameterCount(&T::abortRequest) == 2,
          "Expected abortRequest(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::abortRequest, jsInvoker_, instance_, std::move(requestId));
    }
    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    jsi::Value getSize(jsi::Runtime &rt, jsi::String uri) override {
      static_assert(
          bridging::getParameterCount(&T::getSize) == 2,
          "Expected getSize(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getSize, jsInvoker_, instance_, std::move(uri));
    }
    jsi::Value getSizeWithHeaders(jsi::Runtime &rt, jsi::String uri, jsi::Object headers) override {
      static_assert(
          bridging::getParameterCount(&T::getSizeWithHeaders) == 3,
          "Expected getSizeWithHeaders(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getSizeWithHeaders, jsInvoker_, instance_, std::move(uri), std::move(headers));
    }
    jsi::Value prefetchImage(jsi::Runtime &rt, jsi::String uri, double requestId) override {
      static_assert(
          bridging::getParameterCount(&T::prefetchImage) == 3,
          "Expected prefetchImage(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::prefetchImage, jsInvoker_, instance_, std::move(uri), std::move(requestId));
    }
    jsi::Value queryCache(jsi::Runtime &rt, jsi::Array uris) override {
      static_assert(
          bridging::getParameterCount(&T::queryCache) == 2,
          "Expected queryCache(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::queryCache, jsInvoker_, instance_, std::move(uris));
    }

  private:
    friend class NativeImageLoaderAndroidCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeImageLoaderIOSCxxSpecJSI : public TurboModule {
protected:
  NativeImageLoaderIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual jsi::Value getSize(jsi::Runtime &rt, jsi::String uri) = 0;
  virtual jsi::Value getSizeWithHeaders(jsi::Runtime &rt, jsi::String uri, jsi::Object headers) = 0;
  virtual jsi::Value prefetchImage(jsi::Runtime &rt, jsi::String uri) = 0;
  virtual jsi::Value prefetchImageWithMetadata(jsi::Runtime &rt, jsi::String uri, jsi::String queryRootName, double rootTag) = 0;
  virtual jsi::Value queryCache(jsi::Runtime &rt, jsi::Array uris) = 0;

};

template <typename T>
class JSI_EXPORT NativeImageLoaderIOSCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "ImageLoader";

protected:
  NativeImageLoaderIOSCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeImageLoaderIOSCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeImageLoaderIOSCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeImageLoaderIOSCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    jsi::Value getSize(jsi::Runtime &rt, jsi::String uri) override {
      static_assert(
          bridging::getParameterCount(&T::getSize) == 2,
          "Expected getSize(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getSize, jsInvoker_, instance_, std::move(uri));
    }
    jsi::Value getSizeWithHeaders(jsi::Runtime &rt, jsi::String uri, jsi::Object headers) override {
      static_assert(
          bridging::getParameterCount(&T::getSizeWithHeaders) == 3,
          "Expected getSizeWithHeaders(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getSizeWithHeaders, jsInvoker_, instance_, std::move(uri), std::move(headers));
    }
    jsi::Value prefetchImage(jsi::Runtime &rt, jsi::String uri) override {
      static_assert(
          bridging::getParameterCount(&T::prefetchImage) == 2,
          "Expected prefetchImage(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::prefetchImage, jsInvoker_, instance_, std::move(uri));
    }
    jsi::Value prefetchImageWithMetadata(jsi::Runtime &rt, jsi::String uri, jsi::String queryRootName, double rootTag) override {
      static_assert(
          bridging::getParameterCount(&T::prefetchImageWithMetadata) == 4,
          "Expected prefetchImageWithMetadata(...) to have 4 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::prefetchImageWithMetadata, jsInvoker_, instance_, std::move(uri), std::move(queryRootName), std::move(rootTag));
    }
    jsi::Value queryCache(jsi::Runtime &rt, jsi::Array uris) override {
      static_assert(
          bridging::getParameterCount(&T::queryCache) == 2,
          "Expected queryCache(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::queryCache, jsInvoker_, instance_, std::move(uris));
    }

  private:
    friend class NativeImageLoaderIOSCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeImageStoreAndroidCxxSpecJSI : public TurboModule {
protected:
  NativeImageStoreAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual void getBase64ForTag(jsi::Runtime &rt, jsi::String uri, jsi::Function successCallback, jsi::Function errorCallback) = 0;

};

template <typename T>
class JSI_EXPORT NativeImageStoreAndroidCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "ImageStoreManager";

protected:
  NativeImageStoreAndroidCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeImageStoreAndroidCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeImageStoreAndroidCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeImageStoreAndroidCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    void getBase64ForTag(jsi::Runtime &rt, jsi::String uri, jsi::Function successCallback, jsi::Function errorCallback) override {
      static_assert(
          bridging::getParameterCount(&T::getBase64ForTag) == 4,
          "Expected getBase64ForTag(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getBase64ForTag, jsInvoker_, instance_, std::move(uri), std::move(successCallback), std::move(errorCallback));
    }

  private:
    friend class NativeImageStoreAndroidCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeImageStoreIOSCxxSpecJSI : public TurboModule {
protected:
  NativeImageStoreIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual void getBase64ForTag(jsi::Runtime &rt, jsi::String uri, jsi::Function successCallback, jsi::Function errorCallback) = 0;
  virtual void hasImageForTag(jsi::Runtime &rt, jsi::String uri, jsi::Function callback) = 0;
  virtual void removeImageForTag(jsi::Runtime &rt, jsi::String uri) = 0;
  virtual void addImageFromBase64(jsi::Runtime &rt, jsi::String base64ImageData, jsi::Function successCallback, jsi::Function errorCallback) = 0;

};

template <typename T>
class JSI_EXPORT NativeImageStoreIOSCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "ImageStoreManager";

protected:
  NativeImageStoreIOSCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeImageStoreIOSCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeImageStoreIOSCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeImageStoreIOSCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    void getBase64ForTag(jsi::Runtime &rt, jsi::String uri, jsi::Function successCallback, jsi::Function errorCallback) override {
      static_assert(
          bridging::getParameterCount(&T::getBase64ForTag) == 4,
          "Expected getBase64ForTag(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getBase64ForTag, jsInvoker_, instance_, std::move(uri), std::move(successCallback), std::move(errorCallback));
    }
    void hasImageForTag(jsi::Runtime &rt, jsi::String uri, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::hasImageForTag) == 3,
          "Expected hasImageForTag(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::hasImageForTag, jsInvoker_, instance_, std::move(uri), std::move(callback));
    }
    void removeImageForTag(jsi::Runtime &rt, jsi::String uri) override {
      static_assert(
          bridging::getParameterCount(&T::removeImageForTag) == 2,
          "Expected removeImageForTag(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeImageForTag, jsInvoker_, instance_, std::move(uri));
    }
    void addImageFromBase64(jsi::Runtime &rt, jsi::String base64ImageData, jsi::Function successCallback, jsi::Function errorCallback) override {
      static_assert(
          bridging::getParameterCount(&T::addImageFromBase64) == 4,
          "Expected addImageFromBase64(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addImageFromBase64, jsInvoker_, instance_, std::move(base64ImageData), std::move(successCallback), std::move(errorCallback));
    }

  private:
    friend class NativeImageStoreIOSCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeIntentAndroidCxxSpecJSI : public TurboModule {
protected:
  NativeIntentAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Value getInitialURL(jsi::Runtime &rt) = 0;
  virtual jsi::Value canOpenURL(jsi::Runtime &rt, jsi::String url) = 0;
  virtual jsi::Value openURL(jsi::Runtime &rt, jsi::String url) = 0;
  virtual jsi::Value openSettings(jsi::Runtime &rt) = 0;
  virtual jsi::Value sendIntent(jsi::Runtime &rt, jsi::String action, std::optional<jsi::Array> extras) = 0;

};

template <typename T>
class JSI_EXPORT NativeIntentAndroidCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "IntentAndroid";

protected:
  NativeIntentAndroidCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeIntentAndroidCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeIntentAndroidCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeIntentAndroidCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Value getInitialURL(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getInitialURL) == 1,
          "Expected getInitialURL(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getInitialURL, jsInvoker_, instance_);
    }
    jsi::Value canOpenURL(jsi::Runtime &rt, jsi::String url) override {
      static_assert(
          bridging::getParameterCount(&T::canOpenURL) == 2,
          "Expected canOpenURL(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::canOpenURL, jsInvoker_, instance_, std::move(url));
    }
    jsi::Value openURL(jsi::Runtime &rt, jsi::String url) override {
      static_assert(
          bridging::getParameterCount(&T::openURL) == 2,
          "Expected openURL(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::openURL, jsInvoker_, instance_, std::move(url));
    }
    jsi::Value openSettings(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::openSettings) == 1,
          "Expected openSettings(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::openSettings, jsInvoker_, instance_);
    }
    jsi::Value sendIntent(jsi::Runtime &rt, jsi::String action, std::optional<jsi::Array> extras) override {
      static_assert(
          bridging::getParameterCount(&T::sendIntent) == 3,
          "Expected sendIntent(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::sendIntent, jsInvoker_, instance_, std::move(action), std::move(extras));
    }

  private:
    friend class NativeIntentAndroidCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeJSCHeapCaptureCxxSpecJSI : public TurboModule {
protected:
  NativeJSCHeapCaptureCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void captureComplete(jsi::Runtime &rt, jsi::String path, std::optional<jsi::String> error) = 0;

};

template <typename T>
class JSI_EXPORT NativeJSCHeapCaptureCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "JSCHeapCapture";

protected:
  NativeJSCHeapCaptureCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeJSCHeapCaptureCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeJSCHeapCaptureCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeJSCHeapCaptureCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void captureComplete(jsi::Runtime &rt, jsi::String path, std::optional<jsi::String> error) override {
      static_assert(
          bridging::getParameterCount(&T::captureComplete) == 3,
          "Expected captureComplete(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::captureComplete, jsInvoker_, instance_, std::move(path), std::move(error));
    }

  private:
    friend class NativeJSCHeapCaptureCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeKeyboardObserverCxxSpecJSI : public TurboModule {
protected:
  NativeKeyboardObserverCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void addListener(jsi::Runtime &rt, jsi::String eventName) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;

};

template <typename T>
class JSI_EXPORT NativeKeyboardObserverCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "KeyboardObserver";

protected:
  NativeKeyboardObserverCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeKeyboardObserverCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeKeyboardObserverCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeKeyboardObserverCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void addListener(jsi::Runtime &rt, jsi::String eventName) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventName));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }

  private:
    friend class NativeKeyboardObserverCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeLinkingManagerCxxSpecJSI : public TurboModule {
protected:
  NativeLinkingManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Value getInitialURL(jsi::Runtime &rt) = 0;
  virtual jsi::Value canOpenURL(jsi::Runtime &rt, jsi::String url) = 0;
  virtual jsi::Value openURL(jsi::Runtime &rt, jsi::String url) = 0;
  virtual jsi::Value openSettings(jsi::Runtime &rt) = 0;
  virtual void addListener(jsi::Runtime &rt, jsi::String eventName) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;

};

template <typename T>
class JSI_EXPORT NativeLinkingManagerCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "LinkingManager";

protected:
  NativeLinkingManagerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeLinkingManagerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeLinkingManagerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeLinkingManagerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Value getInitialURL(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getInitialURL) == 1,
          "Expected getInitialURL(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getInitialURL, jsInvoker_, instance_);
    }
    jsi::Value canOpenURL(jsi::Runtime &rt, jsi::String url) override {
      static_assert(
          bridging::getParameterCount(&T::canOpenURL) == 2,
          "Expected canOpenURL(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::canOpenURL, jsInvoker_, instance_, std::move(url));
    }
    jsi::Value openURL(jsi::Runtime &rt, jsi::String url) override {
      static_assert(
          bridging::getParameterCount(&T::openURL) == 2,
          "Expected openURL(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::openURL, jsInvoker_, instance_, std::move(url));
    }
    jsi::Value openSettings(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::openSettings) == 1,
          "Expected openSettings(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::openSettings, jsInvoker_, instance_);
    }
    void addListener(jsi::Runtime &rt, jsi::String eventName) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventName));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }

  private:
    friend class NativeLinkingManagerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeLogBoxCxxSpecJSI : public TurboModule {
protected:
  NativeLogBoxCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void show(jsi::Runtime &rt) = 0;
  virtual void hide(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeLogBoxCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "LogBox";

protected:
  NativeLogBoxCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeLogBoxCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeLogBoxCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeLogBoxCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void show(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::show) == 1,
          "Expected show(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::show, jsInvoker_, instance_);
    }
    void hide(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::hide) == 1,
          "Expected hide(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::hide, jsInvoker_, instance_);
    }

  private:
    friend class NativeLogBoxCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeModalManagerCxxSpecJSI : public TurboModule {
protected:
  NativeModalManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void addListener(jsi::Runtime &rt, jsi::String eventName) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;

};

template <typename T>
class JSI_EXPORT NativeModalManagerCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "ModalManager";

protected:
  NativeModalManagerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeModalManagerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeModalManagerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeModalManagerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void addListener(jsi::Runtime &rt, jsi::String eventName) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventName));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }

  private:
    friend class NativeModalManagerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeNetworkingAndroidCxxSpecJSI : public TurboModule {
protected:
  NativeNetworkingAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void sendRequest(jsi::Runtime &rt, jsi::String method, jsi::String url, double requestId, jsi::Array headers, jsi::Object data, jsi::String responseType, bool useIncrementalUpdates, double timeout, bool withCredentials) = 0;
  virtual void abortRequest(jsi::Runtime &rt, double requestId) = 0;
  virtual void clearCookies(jsi::Runtime &rt, jsi::Function callback) = 0;
  virtual void addListener(jsi::Runtime &rt, jsi::String eventName) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;

};

template <typename T>
class JSI_EXPORT NativeNetworkingAndroidCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "Networking";

protected:
  NativeNetworkingAndroidCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeNetworkingAndroidCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeNetworkingAndroidCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeNetworkingAndroidCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void sendRequest(jsi::Runtime &rt, jsi::String method, jsi::String url, double requestId, jsi::Array headers, jsi::Object data, jsi::String responseType, bool useIncrementalUpdates, double timeout, bool withCredentials) override {
      static_assert(
          bridging::getParameterCount(&T::sendRequest) == 10,
          "Expected sendRequest(...) to have 10 parameters");

      return bridging::callFromJs<void>(
          rt, &T::sendRequest, jsInvoker_, instance_, std::move(method), std::move(url), std::move(requestId), std::move(headers), std::move(data), std::move(responseType), std::move(useIncrementalUpdates), std::move(timeout), std::move(withCredentials));
    }
    void abortRequest(jsi::Runtime &rt, double requestId) override {
      static_assert(
          bridging::getParameterCount(&T::abortRequest) == 2,
          "Expected abortRequest(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::abortRequest, jsInvoker_, instance_, std::move(requestId));
    }
    void clearCookies(jsi::Runtime &rt, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::clearCookies) == 2,
          "Expected clearCookies(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::clearCookies, jsInvoker_, instance_, std::move(callback));
    }
    void addListener(jsi::Runtime &rt, jsi::String eventName) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventName));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }

  private:
    friend class NativeNetworkingAndroidCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeNetworkingIOSCxxSpecJSI : public TurboModule {
protected:
  NativeNetworkingIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void sendRequest(jsi::Runtime &rt, jsi::Object query, jsi::Function callback) = 0;
  virtual void abortRequest(jsi::Runtime &rt, double requestId) = 0;
  virtual void clearCookies(jsi::Runtime &rt, jsi::Function callback) = 0;
  virtual void addListener(jsi::Runtime &rt, jsi::String eventName) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;

};

template <typename T>
class JSI_EXPORT NativeNetworkingIOSCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "Networking";

protected:
  NativeNetworkingIOSCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeNetworkingIOSCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeNetworkingIOSCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeNetworkingIOSCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void sendRequest(jsi::Runtime &rt, jsi::Object query, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::sendRequest) == 3,
          "Expected sendRequest(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::sendRequest, jsInvoker_, instance_, std::move(query), std::move(callback));
    }
    void abortRequest(jsi::Runtime &rt, double requestId) override {
      static_assert(
          bridging::getParameterCount(&T::abortRequest) == 2,
          "Expected abortRequest(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::abortRequest, jsInvoker_, instance_, std::move(requestId));
    }
    void clearCookies(jsi::Runtime &rt, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::clearCookies) == 2,
          "Expected clearCookies(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::clearCookies, jsInvoker_, instance_, std::move(callback));
    }
    void addListener(jsi::Runtime &rt, jsi::String eventName) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventName));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }

  private:
    friend class NativeNetworkingIOSCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativePermissionsAndroidCxxSpecJSI : public TurboModule {
protected:
  NativePermissionsAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Value checkPermission(jsi::Runtime &rt, jsi::String permission) = 0;
  virtual jsi::Value requestPermission(jsi::Runtime &rt, jsi::String permission) = 0;
  virtual jsi::Value shouldShowRequestPermissionRationale(jsi::Runtime &rt, jsi::String permission) = 0;
  virtual jsi::Value requestMultiplePermissions(jsi::Runtime &rt, jsi::Array permissions) = 0;

};

template <typename T>
class JSI_EXPORT NativePermissionsAndroidCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "PermissionsAndroid";

protected:
  NativePermissionsAndroidCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativePermissionsAndroidCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativePermissionsAndroidCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativePermissionsAndroidCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Value checkPermission(jsi::Runtime &rt, jsi::String permission) override {
      static_assert(
          bridging::getParameterCount(&T::checkPermission) == 2,
          "Expected checkPermission(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::checkPermission, jsInvoker_, instance_, std::move(permission));
    }
    jsi::Value requestPermission(jsi::Runtime &rt, jsi::String permission) override {
      static_assert(
          bridging::getParameterCount(&T::requestPermission) == 2,
          "Expected requestPermission(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::requestPermission, jsInvoker_, instance_, std::move(permission));
    }
    jsi::Value shouldShowRequestPermissionRationale(jsi::Runtime &rt, jsi::String permission) override {
      static_assert(
          bridging::getParameterCount(&T::shouldShowRequestPermissionRationale) == 2,
          "Expected shouldShowRequestPermissionRationale(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::shouldShowRequestPermissionRationale, jsInvoker_, instance_, std::move(permission));
    }
    jsi::Value requestMultiplePermissions(jsi::Runtime &rt, jsi::Array permissions) override {
      static_assert(
          bridging::getParameterCount(&T::requestMultiplePermissions) == 2,
          "Expected requestMultiplePermissions(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::requestMultiplePermissions, jsInvoker_, instance_, std::move(permissions));
    }

  private:
    friend class NativePermissionsAndroidCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativePlatformConstantsAndroidPlatformConstantsAndroid

template <typename P0, typename P1, typename P2, typename P3, typename P4, typename P5, typename P6, typename P7, typename P8, typename P9, typename P10, typename P11>
struct NativePlatformConstantsAndroidPlatformConstantsAndroid {
  P0 isTesting;
  P1 isDisableAnimations;
  P2 reactNativeVersion;
  P3 Version;
  P4 Release;
  P5 Serial;
  P6 Fingerprint;
  P7 Model;
  P8 ServerHost;
  P9 uiMode;
  P10 Brand;
  P11 Manufacturer;
  bool operator==(const NativePlatformConstantsAndroidPlatformConstantsAndroid &other) const {
    return isTesting == other.isTesting && isDisableAnimations == other.isDisableAnimations && reactNativeVersion == other.reactNativeVersion && Version == other.Version && Release == other.Release && Serial == other.Serial && Fingerprint == other.Fingerprint && Model == other.Model && ServerHost == other.ServerHost && uiMode == other.uiMode && Brand == other.Brand && Manufacturer == other.Manufacturer;
  }
};

template <typename T>
struct NativePlatformConstantsAndroidPlatformConstantsAndroidBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.isTesting)>(rt, value.getProperty(rt, "isTesting"), jsInvoker),
      bridging::fromJs<decltype(types.isDisableAnimations)>(rt, value.getProperty(rt, "isDisableAnimations"), jsInvoker),
      bridging::fromJs<decltype(types.reactNativeVersion)>(rt, value.getProperty(rt, "reactNativeVersion"), jsInvoker),
      bridging::fromJs<decltype(types.Version)>(rt, value.getProperty(rt, "Version"), jsInvoker),
      bridging::fromJs<decltype(types.Release)>(rt, value.getProperty(rt, "Release"), jsInvoker),
      bridging::fromJs<decltype(types.Serial)>(rt, value.getProperty(rt, "Serial"), jsInvoker),
      bridging::fromJs<decltype(types.Fingerprint)>(rt, value.getProperty(rt, "Fingerprint"), jsInvoker),
      bridging::fromJs<decltype(types.Model)>(rt, value.getProperty(rt, "Model"), jsInvoker),
      bridging::fromJs<decltype(types.ServerHost)>(rt, value.getProperty(rt, "ServerHost"), jsInvoker),
      bridging::fromJs<decltype(types.uiMode)>(rt, value.getProperty(rt, "uiMode"), jsInvoker),
      bridging::fromJs<decltype(types.Brand)>(rt, value.getProperty(rt, "Brand"), jsInvoker),
      bridging::fromJs<decltype(types.Manufacturer)>(rt, value.getProperty(rt, "Manufacturer"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static bool isTestingToJs(jsi::Runtime &rt, decltype(types.isTesting) value) {
    return bridging::toJs(rt, value);
  }

  static bool isDisableAnimationsToJs(jsi::Runtime &rt, decltype(types.isDisableAnimations) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Object reactNativeVersionToJs(jsi::Runtime &rt, decltype(types.reactNativeVersion) value) {
    return bridging::toJs(rt, value);
  }

  static double VersionToJs(jsi::Runtime &rt, decltype(types.Version) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String ReleaseToJs(jsi::Runtime &rt, decltype(types.Release) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String SerialToJs(jsi::Runtime &rt, decltype(types.Serial) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String FingerprintToJs(jsi::Runtime &rt, decltype(types.Fingerprint) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String ModelToJs(jsi::Runtime &rt, decltype(types.Model) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String ServerHostToJs(jsi::Runtime &rt, decltype(types.ServerHost) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String uiModeToJs(jsi::Runtime &rt, decltype(types.uiMode) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String BrandToJs(jsi::Runtime &rt, decltype(types.Brand) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String ManufacturerToJs(jsi::Runtime &rt, decltype(types.Manufacturer) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "isTesting", bridging::toJs(rt, value.isTesting, jsInvoker));
    if (value.isDisableAnimations) {
      result.setProperty(rt, "isDisableAnimations", bridging::toJs(rt, value.isDisableAnimations.value(), jsInvoker));
    }
    result.setProperty(rt, "reactNativeVersion", bridging::toJs(rt, value.reactNativeVersion, jsInvoker));
    result.setProperty(rt, "Version", bridging::toJs(rt, value.Version, jsInvoker));
    result.setProperty(rt, "Release", bridging::toJs(rt, value.Release, jsInvoker));
    result.setProperty(rt, "Serial", bridging::toJs(rt, value.Serial, jsInvoker));
    result.setProperty(rt, "Fingerprint", bridging::toJs(rt, value.Fingerprint, jsInvoker));
    result.setProperty(rt, "Model", bridging::toJs(rt, value.Model, jsInvoker));
    if (value.ServerHost) {
      result.setProperty(rt, "ServerHost", bridging::toJs(rt, value.ServerHost.value(), jsInvoker));
    }
    result.setProperty(rt, "uiMode", bridging::toJs(rt, value.uiMode, jsInvoker));
    result.setProperty(rt, "Brand", bridging::toJs(rt, value.Brand, jsInvoker));
    result.setProperty(rt, "Manufacturer", bridging::toJs(rt, value.Manufacturer, jsInvoker));
    return result;
  }
};



#pragma mark - NativePlatformConstantsAndroidReactNativeVersionAndroid

template <typename P0, typename P1, typename P2, typename P3>
struct NativePlatformConstantsAndroidReactNativeVersionAndroid {
  P0 major;
  P1 minor;
  P2 patch;
  P3 prerelease;
  bool operator==(const NativePlatformConstantsAndroidReactNativeVersionAndroid &other) const {
    return major == other.major && minor == other.minor && patch == other.patch && prerelease == other.prerelease;
  }
};

template <typename T>
struct NativePlatformConstantsAndroidReactNativeVersionAndroidBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.major)>(rt, value.getProperty(rt, "major"), jsInvoker),
      bridging::fromJs<decltype(types.minor)>(rt, value.getProperty(rt, "minor"), jsInvoker),
      bridging::fromJs<decltype(types.patch)>(rt, value.getProperty(rt, "patch"), jsInvoker),
      bridging::fromJs<decltype(types.prerelease)>(rt, value.getProperty(rt, "prerelease"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static double majorToJs(jsi::Runtime &rt, decltype(types.major) value) {
    return bridging::toJs(rt, value);
  }

  static double minorToJs(jsi::Runtime &rt, decltype(types.minor) value) {
    return bridging::toJs(rt, value);
  }

  static double patchToJs(jsi::Runtime &rt, decltype(types.patch) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> prereleaseToJs(jsi::Runtime &rt, decltype(types.prerelease) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "major", bridging::toJs(rt, value.major, jsInvoker));
    result.setProperty(rt, "minor", bridging::toJs(rt, value.minor, jsInvoker));
    result.setProperty(rt, "patch", bridging::toJs(rt, value.patch, jsInvoker));
    result.setProperty(rt, "prerelease", bridging::toJs(rt, value.prerelease, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativePlatformConstantsAndroidCxxSpecJSI : public TurboModule {
protected:
  NativePlatformConstantsAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual jsi::String getAndroidID(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativePlatformConstantsAndroidCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "PlatformConstants";

protected:
  NativePlatformConstantsAndroidCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativePlatformConstantsAndroidCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativePlatformConstantsAndroidCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativePlatformConstantsAndroidCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    jsi::String getAndroidID(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getAndroidID) == 1,
          "Expected getAndroidID(...) to have 1 parameters");

      return bridging::callFromJs<jsi::String>(
          rt, &T::getAndroidID, jsInvoker_, instance_);
    }

  private:
    friend class NativePlatformConstantsAndroidCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativePlatformConstantsIOSPlatformConstantsIOS

template <typename P0, typename P1, typename P2, typename P3, typename P4, typename P5, typename P6, typename P7>
struct NativePlatformConstantsIOSPlatformConstantsIOS {
  P0 isTesting;
  P1 isDisableAnimations;
  P2 reactNativeVersion;
  P3 forceTouchAvailable;
  P4 osVersion;
  P5 systemName;
  P6 interfaceIdiom;
  P7 isMacCatalyst;
  bool operator==(const NativePlatformConstantsIOSPlatformConstantsIOS &other) const {
    return isTesting == other.isTesting && isDisableAnimations == other.isDisableAnimations && reactNativeVersion == other.reactNativeVersion && forceTouchAvailable == other.forceTouchAvailable && osVersion == other.osVersion && systemName == other.systemName && interfaceIdiom == other.interfaceIdiom && isMacCatalyst == other.isMacCatalyst;
  }
};

template <typename T>
struct NativePlatformConstantsIOSPlatformConstantsIOSBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.isTesting)>(rt, value.getProperty(rt, "isTesting"), jsInvoker),
      bridging::fromJs<decltype(types.isDisableAnimations)>(rt, value.getProperty(rt, "isDisableAnimations"), jsInvoker),
      bridging::fromJs<decltype(types.reactNativeVersion)>(rt, value.getProperty(rt, "reactNativeVersion"), jsInvoker),
      bridging::fromJs<decltype(types.forceTouchAvailable)>(rt, value.getProperty(rt, "forceTouchAvailable"), jsInvoker),
      bridging::fromJs<decltype(types.osVersion)>(rt, value.getProperty(rt, "osVersion"), jsInvoker),
      bridging::fromJs<decltype(types.systemName)>(rt, value.getProperty(rt, "systemName"), jsInvoker),
      bridging::fromJs<decltype(types.interfaceIdiom)>(rt, value.getProperty(rt, "interfaceIdiom"), jsInvoker),
      bridging::fromJs<decltype(types.isMacCatalyst)>(rt, value.getProperty(rt, "isMacCatalyst"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static bool isTestingToJs(jsi::Runtime &rt, decltype(types.isTesting) value) {
    return bridging::toJs(rt, value);
  }

  static bool isDisableAnimationsToJs(jsi::Runtime &rt, decltype(types.isDisableAnimations) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Object reactNativeVersionToJs(jsi::Runtime &rt, decltype(types.reactNativeVersion) value) {
    return bridging::toJs(rt, value);
  }

  static bool forceTouchAvailableToJs(jsi::Runtime &rt, decltype(types.forceTouchAvailable) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String osVersionToJs(jsi::Runtime &rt, decltype(types.osVersion) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String systemNameToJs(jsi::Runtime &rt, decltype(types.systemName) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::String interfaceIdiomToJs(jsi::Runtime &rt, decltype(types.interfaceIdiom) value) {
    return bridging::toJs(rt, value);
  }

  static bool isMacCatalystToJs(jsi::Runtime &rt, decltype(types.isMacCatalyst) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "isTesting", bridging::toJs(rt, value.isTesting, jsInvoker));
    if (value.isDisableAnimations) {
      result.setProperty(rt, "isDisableAnimations", bridging::toJs(rt, value.isDisableAnimations.value(), jsInvoker));
    }
    result.setProperty(rt, "reactNativeVersion", bridging::toJs(rt, value.reactNativeVersion, jsInvoker));
    result.setProperty(rt, "forceTouchAvailable", bridging::toJs(rt, value.forceTouchAvailable, jsInvoker));
    result.setProperty(rt, "osVersion", bridging::toJs(rt, value.osVersion, jsInvoker));
    result.setProperty(rt, "systemName", bridging::toJs(rt, value.systemName, jsInvoker));
    result.setProperty(rt, "interfaceIdiom", bridging::toJs(rt, value.interfaceIdiom, jsInvoker));
    if (value.isMacCatalyst) {
      result.setProperty(rt, "isMacCatalyst", bridging::toJs(rt, value.isMacCatalyst.value(), jsInvoker));
    }
    return result;
  }
};

class JSI_EXPORT NativePlatformConstantsIOSCxxSpecJSI : public TurboModule {
protected:
  NativePlatformConstantsIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativePlatformConstantsIOSCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "PlatformConstants";

protected:
  NativePlatformConstantsIOSCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativePlatformConstantsIOSCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativePlatformConstantsIOSCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativePlatformConstantsIOSCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }

  private:
    friend class NativePlatformConstantsIOSCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativePushNotificationManagerIOSNotification

template <typename P0, typename P1, typename P2, typename P3, typename P4, typename P5, typename P6, typename P7, typename P8>
struct NativePushNotificationManagerIOSNotification {
  P0 alertTitle;
  P1 alertBody;
  P2 userInfo;
  P3 category;
  P4 fireDate;
  P5 fireIntervalSeconds;
  P6 applicationIconBadgeNumber;
  P7 isSilent;
  P8 soundName;
  bool operator==(const NativePushNotificationManagerIOSNotification &other) const {
    return alertTitle == other.alertTitle && alertBody == other.alertBody && userInfo == other.userInfo && category == other.category && fireDate == other.fireDate && fireIntervalSeconds == other.fireIntervalSeconds && applicationIconBadgeNumber == other.applicationIconBadgeNumber && isSilent == other.isSilent && soundName == other.soundName;
  }
};

template <typename T>
struct NativePushNotificationManagerIOSNotificationBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.alertTitle)>(rt, value.getProperty(rt, "alertTitle"), jsInvoker),
      bridging::fromJs<decltype(types.alertBody)>(rt, value.getProperty(rt, "alertBody"), jsInvoker),
      bridging::fromJs<decltype(types.userInfo)>(rt, value.getProperty(rt, "userInfo"), jsInvoker),
      bridging::fromJs<decltype(types.category)>(rt, value.getProperty(rt, "category"), jsInvoker),
      bridging::fromJs<decltype(types.fireDate)>(rt, value.getProperty(rt, "fireDate"), jsInvoker),
      bridging::fromJs<decltype(types.fireIntervalSeconds)>(rt, value.getProperty(rt, "fireIntervalSeconds"), jsInvoker),
      bridging::fromJs<decltype(types.applicationIconBadgeNumber)>(rt, value.getProperty(rt, "applicationIconBadgeNumber"), jsInvoker),
      bridging::fromJs<decltype(types.isSilent)>(rt, value.getProperty(rt, "isSilent"), jsInvoker),
      bridging::fromJs<decltype(types.soundName)>(rt, value.getProperty(rt, "soundName"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static std::optional<jsi::String> alertTitleToJs(jsi::Runtime &rt, decltype(types.alertTitle) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> alertBodyToJs(jsi::Runtime &rt, decltype(types.alertBody) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::Object> userInfoToJs(jsi::Runtime &rt, decltype(types.userInfo) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> categoryToJs(jsi::Runtime &rt, decltype(types.category) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<double> fireDateToJs(jsi::Runtime &rt, decltype(types.fireDate) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<double> fireIntervalSecondsToJs(jsi::Runtime &rt, decltype(types.fireIntervalSeconds) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<double> applicationIconBadgeNumberToJs(jsi::Runtime &rt, decltype(types.applicationIconBadgeNumber) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<bool> isSilentToJs(jsi::Runtime &rt, decltype(types.isSilent) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::String> soundNameToJs(jsi::Runtime &rt, decltype(types.soundName) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    if (value.alertTitle) {
      result.setProperty(rt, "alertTitle", bridging::toJs(rt, value.alertTitle.value(), jsInvoker));
    }
    if (value.alertBody) {
      result.setProperty(rt, "alertBody", bridging::toJs(rt, value.alertBody.value(), jsInvoker));
    }
    if (value.userInfo) {
      result.setProperty(rt, "userInfo", bridging::toJs(rt, value.userInfo.value(), jsInvoker));
    }
    if (value.category) {
      result.setProperty(rt, "category", bridging::toJs(rt, value.category.value(), jsInvoker));
    }
    if (value.fireDate) {
      result.setProperty(rt, "fireDate", bridging::toJs(rt, value.fireDate.value(), jsInvoker));
    }
    if (value.fireIntervalSeconds) {
      result.setProperty(rt, "fireIntervalSeconds", bridging::toJs(rt, value.fireIntervalSeconds.value(), jsInvoker));
    }
    if (value.applicationIconBadgeNumber) {
      result.setProperty(rt, "applicationIconBadgeNumber", bridging::toJs(rt, value.applicationIconBadgeNumber.value(), jsInvoker));
    }
    if (value.isSilent) {
      result.setProperty(rt, "isSilent", bridging::toJs(rt, value.isSilent.value(), jsInvoker));
    }
    if (value.soundName) {
      result.setProperty(rt, "soundName", bridging::toJs(rt, value.soundName.value(), jsInvoker));
    }
    return result;
  }
};



#pragma mark - NativePushNotificationManagerIOSPermissions

template <typename P0, typename P1, typename P2>
struct NativePushNotificationManagerIOSPermissions {
  P0 alert;
  P1 badge;
  P2 sound;
  bool operator==(const NativePushNotificationManagerIOSPermissions &other) const {
    return alert == other.alert && badge == other.badge && sound == other.sound;
  }
};

template <typename T>
struct NativePushNotificationManagerIOSPermissionsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.alert)>(rt, value.getProperty(rt, "alert"), jsInvoker),
      bridging::fromJs<decltype(types.badge)>(rt, value.getProperty(rt, "badge"), jsInvoker),
      bridging::fromJs<decltype(types.sound)>(rt, value.getProperty(rt, "sound"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static bool alertToJs(jsi::Runtime &rt, decltype(types.alert) value) {
    return bridging::toJs(rt, value);
  }

  static bool badgeToJs(jsi::Runtime &rt, decltype(types.badge) value) {
    return bridging::toJs(rt, value);
  }

  static bool soundToJs(jsi::Runtime &rt, decltype(types.sound) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "alert", bridging::toJs(rt, value.alert, jsInvoker));
    result.setProperty(rt, "badge", bridging::toJs(rt, value.badge, jsInvoker));
    result.setProperty(rt, "sound", bridging::toJs(rt, value.sound, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativePushNotificationManagerIOSCxxSpecJSI : public TurboModule {
protected:
  NativePushNotificationManagerIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual void onFinishRemoteNotification(jsi::Runtime &rt, jsi::String notificationId, jsi::String fetchResult) = 0;
  virtual void setApplicationIconBadgeNumber(jsi::Runtime &rt, double num) = 0;
  virtual void getApplicationIconBadgeNumber(jsi::Runtime &rt, jsi::Function callback) = 0;
  virtual jsi::Value requestPermissions(jsi::Runtime &rt, jsi::Object permission) = 0;
  virtual void abandonPermissions(jsi::Runtime &rt) = 0;
  virtual void checkPermissions(jsi::Runtime &rt, jsi::Function callback) = 0;
  virtual void presentLocalNotification(jsi::Runtime &rt, jsi::Object notification) = 0;
  virtual void scheduleLocalNotification(jsi::Runtime &rt, jsi::Object notification) = 0;
  virtual void cancelAllLocalNotifications(jsi::Runtime &rt) = 0;
  virtual void cancelLocalNotifications(jsi::Runtime &rt, jsi::Object userInfo) = 0;
  virtual jsi::Value getInitialNotification(jsi::Runtime &rt) = 0;
  virtual void getScheduledLocalNotifications(jsi::Runtime &rt, jsi::Function callback) = 0;
  virtual void removeAllDeliveredNotifications(jsi::Runtime &rt) = 0;
  virtual void removeDeliveredNotifications(jsi::Runtime &rt, jsi::Array identifiers) = 0;
  virtual void getDeliveredNotifications(jsi::Runtime &rt, jsi::Function callback) = 0;
  virtual void getAuthorizationStatus(jsi::Runtime &rt, jsi::Function callback) = 0;
  virtual void addListener(jsi::Runtime &rt, jsi::String eventType) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;

};

template <typename T>
class JSI_EXPORT NativePushNotificationManagerIOSCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "PushNotificationManager";

protected:
  NativePushNotificationManagerIOSCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativePushNotificationManagerIOSCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativePushNotificationManagerIOSCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativePushNotificationManagerIOSCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    void onFinishRemoteNotification(jsi::Runtime &rt, jsi::String notificationId, jsi::String fetchResult) override {
      static_assert(
          bridging::getParameterCount(&T::onFinishRemoteNotification) == 3,
          "Expected onFinishRemoteNotification(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::onFinishRemoteNotification, jsInvoker_, instance_, std::move(notificationId), std::move(fetchResult));
    }
    void setApplicationIconBadgeNumber(jsi::Runtime &rt, double num) override {
      static_assert(
          bridging::getParameterCount(&T::setApplicationIconBadgeNumber) == 2,
          "Expected setApplicationIconBadgeNumber(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setApplicationIconBadgeNumber, jsInvoker_, instance_, std::move(num));
    }
    void getApplicationIconBadgeNumber(jsi::Runtime &rt, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::getApplicationIconBadgeNumber) == 2,
          "Expected getApplicationIconBadgeNumber(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getApplicationIconBadgeNumber, jsInvoker_, instance_, std::move(callback));
    }
    jsi::Value requestPermissions(jsi::Runtime &rt, jsi::Object permission) override {
      static_assert(
          bridging::getParameterCount(&T::requestPermissions) == 2,
          "Expected requestPermissions(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::requestPermissions, jsInvoker_, instance_, std::move(permission));
    }
    void abandonPermissions(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::abandonPermissions) == 1,
          "Expected abandonPermissions(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::abandonPermissions, jsInvoker_, instance_);
    }
    void checkPermissions(jsi::Runtime &rt, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::checkPermissions) == 2,
          "Expected checkPermissions(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::checkPermissions, jsInvoker_, instance_, std::move(callback));
    }
    void presentLocalNotification(jsi::Runtime &rt, jsi::Object notification) override {
      static_assert(
          bridging::getParameterCount(&T::presentLocalNotification) == 2,
          "Expected presentLocalNotification(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::presentLocalNotification, jsInvoker_, instance_, std::move(notification));
    }
    void scheduleLocalNotification(jsi::Runtime &rt, jsi::Object notification) override {
      static_assert(
          bridging::getParameterCount(&T::scheduleLocalNotification) == 2,
          "Expected scheduleLocalNotification(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::scheduleLocalNotification, jsInvoker_, instance_, std::move(notification));
    }
    void cancelAllLocalNotifications(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::cancelAllLocalNotifications) == 1,
          "Expected cancelAllLocalNotifications(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::cancelAllLocalNotifications, jsInvoker_, instance_);
    }
    void cancelLocalNotifications(jsi::Runtime &rt, jsi::Object userInfo) override {
      static_assert(
          bridging::getParameterCount(&T::cancelLocalNotifications) == 2,
          "Expected cancelLocalNotifications(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::cancelLocalNotifications, jsInvoker_, instance_, std::move(userInfo));
    }
    jsi::Value getInitialNotification(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getInitialNotification) == 1,
          "Expected getInitialNotification(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getInitialNotification, jsInvoker_, instance_);
    }
    void getScheduledLocalNotifications(jsi::Runtime &rt, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::getScheduledLocalNotifications) == 2,
          "Expected getScheduledLocalNotifications(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getScheduledLocalNotifications, jsInvoker_, instance_, std::move(callback));
    }
    void removeAllDeliveredNotifications(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::removeAllDeliveredNotifications) == 1,
          "Expected removeAllDeliveredNotifications(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeAllDeliveredNotifications, jsInvoker_, instance_);
    }
    void removeDeliveredNotifications(jsi::Runtime &rt, jsi::Array identifiers) override {
      static_assert(
          bridging::getParameterCount(&T::removeDeliveredNotifications) == 2,
          "Expected removeDeliveredNotifications(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeDeliveredNotifications, jsInvoker_, instance_, std::move(identifiers));
    }
    void getDeliveredNotifications(jsi::Runtime &rt, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::getDeliveredNotifications) == 2,
          "Expected getDeliveredNotifications(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getDeliveredNotifications, jsInvoker_, instance_, std::move(callback));
    }
    void getAuthorizationStatus(jsi::Runtime &rt, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::getAuthorizationStatus) == 2,
          "Expected getAuthorizationStatus(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getAuthorizationStatus, jsInvoker_, instance_, std::move(callback));
    }
    void addListener(jsi::Runtime &rt, jsi::String eventType) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventType));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }

  private:
    friend class NativePushNotificationManagerIOSCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeReactDevToolsSettingsManagerCxxSpecJSI : public TurboModule {
protected:
  NativeReactDevToolsSettingsManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void setGlobalHookSettings(jsi::Runtime &rt, jsi::String settings) = 0;
  virtual std::optional<jsi::String> getGlobalHookSettings(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeReactDevToolsSettingsManagerCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "ReactDevToolsSettingsManager";

protected:
  NativeReactDevToolsSettingsManagerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeReactDevToolsSettingsManagerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeReactDevToolsSettingsManagerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeReactDevToolsSettingsManagerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void setGlobalHookSettings(jsi::Runtime &rt, jsi::String settings) override {
      static_assert(
          bridging::getParameterCount(&T::setGlobalHookSettings) == 2,
          "Expected setGlobalHookSettings(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setGlobalHookSettings, jsInvoker_, instance_, std::move(settings));
    }
    std::optional<jsi::String> getGlobalHookSettings(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getGlobalHookSettings) == 1,
          "Expected getGlobalHookSettings(...) to have 1 parameters");

      return bridging::callFromJs<std::optional<jsi::String>>(
          rt, &T::getGlobalHookSettings, jsInvoker_, instance_);
    }

  private:
    friend class NativeReactDevToolsSettingsManagerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeRedBoxCxxSpecJSI : public TurboModule {
protected:
  NativeRedBoxCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void setExtraData(jsi::Runtime &rt, jsi::Object extraData, jsi::String forIdentifier) = 0;
  virtual void dismiss(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeRedBoxCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "RedBox";

protected:
  NativeRedBoxCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeRedBoxCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeRedBoxCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeRedBoxCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void setExtraData(jsi::Runtime &rt, jsi::Object extraData, jsi::String forIdentifier) override {
      static_assert(
          bridging::getParameterCount(&T::setExtraData) == 3,
          "Expected setExtraData(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setExtraData, jsInvoker_, instance_, std::move(extraData), std::move(forIdentifier));
    }
    void dismiss(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::dismiss) == 1,
          "Expected dismiss(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::dismiss, jsInvoker_, instance_);
    }

  private:
    friend class NativeRedBoxCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeSegmentFetcherCxxSpecJSI : public TurboModule {
protected:
  NativeSegmentFetcherCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void fetchSegment(jsi::Runtime &rt, double segmentId, jsi::Object options, jsi::Function callback) = 0;
  virtual void getSegment(jsi::Runtime &rt, double segmentId, jsi::Object options, jsi::Function callback) = 0;

};

template <typename T>
class JSI_EXPORT NativeSegmentFetcherCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "SegmentFetcher";

protected:
  NativeSegmentFetcherCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeSegmentFetcherCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeSegmentFetcherCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeSegmentFetcherCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void fetchSegment(jsi::Runtime &rt, double segmentId, jsi::Object options, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::fetchSegment) == 4,
          "Expected fetchSegment(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::fetchSegment, jsInvoker_, instance_, std::move(segmentId), std::move(options), std::move(callback));
    }
    void getSegment(jsi::Runtime &rt, double segmentId, jsi::Object options, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::getSegment) == 4,
          "Expected getSegment(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getSegment, jsInvoker_, instance_, std::move(segmentId), std::move(options), std::move(callback));
    }

  private:
    friend class NativeSegmentFetcherCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeSettingsManagerCxxSpecJSI : public TurboModule {
protected:
  NativeSettingsManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual void setValues(jsi::Runtime &rt, jsi::Object values) = 0;
  virtual void deleteValues(jsi::Runtime &rt, jsi::Array values) = 0;

};

template <typename T>
class JSI_EXPORT NativeSettingsManagerCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "SettingsManager";

protected:
  NativeSettingsManagerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeSettingsManagerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeSettingsManagerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeSettingsManagerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    void setValues(jsi::Runtime &rt, jsi::Object values) override {
      static_assert(
          bridging::getParameterCount(&T::setValues) == 2,
          "Expected setValues(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setValues, jsInvoker_, instance_, std::move(values));
    }
    void deleteValues(jsi::Runtime &rt, jsi::Array values) override {
      static_assert(
          bridging::getParameterCount(&T::deleteValues) == 2,
          "Expected deleteValues(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::deleteValues, jsInvoker_, instance_, std::move(values));
    }

  private:
    friend class NativeSettingsManagerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeShareModuleCxxSpecJSI : public TurboModule {
protected:
  NativeShareModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual jsi::Value share(jsi::Runtime &rt, jsi::Object content, std::optional<jsi::String> dialogTitle) = 0;

};

template <typename T>
class JSI_EXPORT NativeShareModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "ShareModule";

protected:
  NativeShareModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeShareModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeShareModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeShareModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    jsi::Value share(jsi::Runtime &rt, jsi::Object content, std::optional<jsi::String> dialogTitle) override {
      static_assert(
          bridging::getParameterCount(&T::share) == 3,
          "Expected share(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::share, jsInvoker_, instance_, std::move(content), std::move(dialogTitle));
    }

  private:
    friend class NativeShareModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeSoundManagerCxxSpecJSI : public TurboModule {
protected:
  NativeSoundManagerCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void playTouchSound(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeSoundManagerCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "SoundManager";

protected:
  NativeSoundManagerCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeSoundManagerCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeSoundManagerCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeSoundManagerCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void playTouchSound(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::playTouchSound) == 1,
          "Expected playTouchSound(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::playTouchSound, jsInvoker_, instance_);
    }

  private:
    friend class NativeSoundManagerCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeSourceCodeSourceCodeConstants

template <typename P0>
struct NativeSourceCodeSourceCodeConstants {
  P0 scriptURL;
  bool operator==(const NativeSourceCodeSourceCodeConstants &other) const {
    return scriptURL == other.scriptURL;
  }
};

template <typename T>
struct NativeSourceCodeSourceCodeConstantsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.scriptURL)>(rt, value.getProperty(rt, "scriptURL"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::String scriptURLToJs(jsi::Runtime &rt, decltype(types.scriptURL) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "scriptURL", bridging::toJs(rt, value.scriptURL, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativeSourceCodeCxxSpecJSI : public TurboModule {
protected:
  NativeSourceCodeCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeSourceCodeCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "SourceCode";

protected:
  NativeSourceCodeCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeSourceCodeCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeSourceCodeCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeSourceCodeCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }

  private:
    friend class NativeSourceCodeCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeStatusBarManagerAndroidCxxSpecJSI : public TurboModule {
protected:
  NativeStatusBarManagerAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual void setColor(jsi::Runtime &rt, double color, bool animated) = 0;
  virtual void setTranslucent(jsi::Runtime &rt, bool translucent) = 0;
  virtual void setStyle(jsi::Runtime &rt, std::optional<jsi::String> statusBarStyle) = 0;
  virtual void setHidden(jsi::Runtime &rt, bool hidden) = 0;

};

template <typename T>
class JSI_EXPORT NativeStatusBarManagerAndroidCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "StatusBarManager";

protected:
  NativeStatusBarManagerAndroidCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeStatusBarManagerAndroidCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeStatusBarManagerAndroidCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeStatusBarManagerAndroidCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    void setColor(jsi::Runtime &rt, double color, bool animated) override {
      static_assert(
          bridging::getParameterCount(&T::setColor) == 3,
          "Expected setColor(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setColor, jsInvoker_, instance_, std::move(color), std::move(animated));
    }
    void setTranslucent(jsi::Runtime &rt, bool translucent) override {
      static_assert(
          bridging::getParameterCount(&T::setTranslucent) == 2,
          "Expected setTranslucent(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setTranslucent, jsInvoker_, instance_, std::move(translucent));
    }
    void setStyle(jsi::Runtime &rt, std::optional<jsi::String> statusBarStyle) override {
      static_assert(
          bridging::getParameterCount(&T::setStyle) == 2,
          "Expected setStyle(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setStyle, jsInvoker_, instance_, std::move(statusBarStyle));
    }
    void setHidden(jsi::Runtime &rt, bool hidden) override {
      static_assert(
          bridging::getParameterCount(&T::setHidden) == 2,
          "Expected setHidden(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setHidden, jsInvoker_, instance_, std::move(hidden));
    }

  private:
    friend class NativeStatusBarManagerAndroidCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeStatusBarManagerIOSCxxSpecJSI : public TurboModule {
protected:
  NativeStatusBarManagerIOSCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual void getHeight(jsi::Runtime &rt, jsi::Function callback) = 0;
  virtual void setNetworkActivityIndicatorVisible(jsi::Runtime &rt, bool visible) = 0;
  virtual void addListener(jsi::Runtime &rt, jsi::String eventType) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;
  virtual void setStyle(jsi::Runtime &rt, std::optional<jsi::String> statusBarStyle, bool animated) = 0;
  virtual void setHidden(jsi::Runtime &rt, bool hidden, jsi::String withAnimation) = 0;

};

template <typename T>
class JSI_EXPORT NativeStatusBarManagerIOSCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "StatusBarManager";

protected:
  NativeStatusBarManagerIOSCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeStatusBarManagerIOSCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeStatusBarManagerIOSCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeStatusBarManagerIOSCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    void getHeight(jsi::Runtime &rt, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::getHeight) == 2,
          "Expected getHeight(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::getHeight, jsInvoker_, instance_, std::move(callback));
    }
    void setNetworkActivityIndicatorVisible(jsi::Runtime &rt, bool visible) override {
      static_assert(
          bridging::getParameterCount(&T::setNetworkActivityIndicatorVisible) == 2,
          "Expected setNetworkActivityIndicatorVisible(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setNetworkActivityIndicatorVisible, jsInvoker_, instance_, std::move(visible));
    }
    void addListener(jsi::Runtime &rt, jsi::String eventType) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventType));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }
    void setStyle(jsi::Runtime &rt, std::optional<jsi::String> statusBarStyle, bool animated) override {
      static_assert(
          bridging::getParameterCount(&T::setStyle) == 3,
          "Expected setStyle(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setStyle, jsInvoker_, instance_, std::move(statusBarStyle), std::move(animated));
    }
    void setHidden(jsi::Runtime &rt, bool hidden, jsi::String withAnimation) override {
      static_assert(
          bridging::getParameterCount(&T::setHidden) == 3,
          "Expected setHidden(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setHidden, jsInvoker_, instance_, std::move(hidden), std::move(withAnimation));
    }

  private:
    friend class NativeStatusBarManagerIOSCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeTimingCxxSpecJSI : public TurboModule {
protected:
  NativeTimingCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void createTimer(jsi::Runtime &rt, double callbackID, double duration, double jsSchedulingTime, bool repeats) = 0;
  virtual void deleteTimer(jsi::Runtime &rt, double timerID) = 0;
  virtual void setSendIdleEvents(jsi::Runtime &rt, bool sendIdleEvents) = 0;

};

template <typename T>
class JSI_EXPORT NativeTimingCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "Timing";

protected:
  NativeTimingCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeTimingCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeTimingCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeTimingCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void createTimer(jsi::Runtime &rt, double callbackID, double duration, double jsSchedulingTime, bool repeats) override {
      static_assert(
          bridging::getParameterCount(&T::createTimer) == 5,
          "Expected createTimer(...) to have 5 parameters");

      return bridging::callFromJs<void>(
          rt, &T::createTimer, jsInvoker_, instance_, std::move(callbackID), std::move(duration), std::move(jsSchedulingTime), std::move(repeats));
    }
    void deleteTimer(jsi::Runtime &rt, double timerID) override {
      static_assert(
          bridging::getParameterCount(&T::deleteTimer) == 2,
          "Expected deleteTimer(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::deleteTimer, jsInvoker_, instance_, std::move(timerID));
    }
    void setSendIdleEvents(jsi::Runtime &rt, bool sendIdleEvents) override {
      static_assert(
          bridging::getParameterCount(&T::setSendIdleEvents) == 2,
          "Expected setSendIdleEvents(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setSendIdleEvents, jsInvoker_, instance_, std::move(sendIdleEvents));
    }

  private:
    friend class NativeTimingCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeToastAndroidCxxSpecJSI : public TurboModule {
protected:
  NativeToastAndroidCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual void show(jsi::Runtime &rt, jsi::String message, double duration) = 0;
  virtual void showWithGravity(jsi::Runtime &rt, jsi::String message, double duration, double gravity) = 0;
  virtual void showWithGravityAndOffset(jsi::Runtime &rt, jsi::String message, double duration, double gravity, double xOffset, double yOffset) = 0;

};

template <typename T>
class JSI_EXPORT NativeToastAndroidCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "ToastAndroid";

protected:
  NativeToastAndroidCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeToastAndroidCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeToastAndroidCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeToastAndroidCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    void show(jsi::Runtime &rt, jsi::String message, double duration) override {
      static_assert(
          bridging::getParameterCount(&T::show) == 3,
          "Expected show(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::show, jsInvoker_, instance_, std::move(message), std::move(duration));
    }
    void showWithGravity(jsi::Runtime &rt, jsi::String message, double duration, double gravity) override {
      static_assert(
          bridging::getParameterCount(&T::showWithGravity) == 4,
          "Expected showWithGravity(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::showWithGravity, jsInvoker_, instance_, std::move(message), std::move(duration), std::move(gravity));
    }
    void showWithGravityAndOffset(jsi::Runtime &rt, jsi::String message, double duration, double gravity, double xOffset, double yOffset) override {
      static_assert(
          bridging::getParameterCount(&T::showWithGravityAndOffset) == 6,
          "Expected showWithGravityAndOffset(...) to have 6 parameters");

      return bridging::callFromJs<void>(
          rt, &T::showWithGravityAndOffset, jsInvoker_, instance_, std::move(message), std::move(duration), std::move(gravity), std::move(xOffset), std::move(yOffset));
    }

  private:
    friend class NativeToastAndroidCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeVibrationCxxSpecJSI : public TurboModule {
protected:
  NativeVibrationCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Object getConstants(jsi::Runtime &rt) = 0;
  virtual void vibrate(jsi::Runtime &rt, double pattern) = 0;
  virtual void vibrateByPattern(jsi::Runtime &rt, jsi::Array pattern, double repeat) = 0;
  virtual void cancel(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeVibrationCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "Vibration";

protected:
  NativeVibrationCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeVibrationCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeVibrationCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeVibrationCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Object getConstants(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getConstants) == 1,
          "Expected getConstants(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getConstants, jsInvoker_, instance_);
    }
    void vibrate(jsi::Runtime &rt, double pattern) override {
      static_assert(
          bridging::getParameterCount(&T::vibrate) == 2,
          "Expected vibrate(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::vibrate, jsInvoker_, instance_, std::move(pattern));
    }
    void vibrateByPattern(jsi::Runtime &rt, jsi::Array pattern, double repeat) override {
      static_assert(
          bridging::getParameterCount(&T::vibrateByPattern) == 3,
          "Expected vibrateByPattern(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::vibrateByPattern, jsInvoker_, instance_, std::move(pattern), std::move(repeat));
    }
    void cancel(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::cancel) == 1,
          "Expected cancel(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::cancel, jsInvoker_, instance_);
    }

  private:
    friend class NativeVibrationCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeWebSocketModuleCxxSpecJSI : public TurboModule {
protected:
  NativeWebSocketModuleCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void connect(jsi::Runtime &rt, jsi::String url, std::optional<jsi::Array> protocols, jsi::Object options, double socketID) = 0;
  virtual void send(jsi::Runtime &rt, jsi::String message, double forSocketID) = 0;
  virtual void sendBinary(jsi::Runtime &rt, jsi::String base64String, double forSocketID) = 0;
  virtual void ping(jsi::Runtime &rt, double socketID) = 0;
  virtual void close(jsi::Runtime &rt, double code, jsi::String reason, double socketID) = 0;
  virtual void addListener(jsi::Runtime &rt, jsi::String eventName) = 0;
  virtual void removeListeners(jsi::Runtime &rt, double count) = 0;

};

template <typename T>
class JSI_EXPORT NativeWebSocketModuleCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "WebSocketModule";

protected:
  NativeWebSocketModuleCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeWebSocketModuleCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeWebSocketModuleCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeWebSocketModuleCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void connect(jsi::Runtime &rt, jsi::String url, std::optional<jsi::Array> protocols, jsi::Object options, double socketID) override {
      static_assert(
          bridging::getParameterCount(&T::connect) == 5,
          "Expected connect(...) to have 5 parameters");

      return bridging::callFromJs<void>(
          rt, &T::connect, jsInvoker_, instance_, std::move(url), std::move(protocols), std::move(options), std::move(socketID));
    }
    void send(jsi::Runtime &rt, jsi::String message, double forSocketID) override {
      static_assert(
          bridging::getParameterCount(&T::send) == 3,
          "Expected send(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::send, jsInvoker_, instance_, std::move(message), std::move(forSocketID));
    }
    void sendBinary(jsi::Runtime &rt, jsi::String base64String, double forSocketID) override {
      static_assert(
          bridging::getParameterCount(&T::sendBinary) == 3,
          "Expected sendBinary(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::sendBinary, jsInvoker_, instance_, std::move(base64String), std::move(forSocketID));
    }
    void ping(jsi::Runtime &rt, double socketID) override {
      static_assert(
          bridging::getParameterCount(&T::ping) == 2,
          "Expected ping(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::ping, jsInvoker_, instance_, std::move(socketID));
    }
    void close(jsi::Runtime &rt, double code, jsi::String reason, double socketID) override {
      static_assert(
          bridging::getParameterCount(&T::close) == 4,
          "Expected close(...) to have 4 parameters");

      return bridging::callFromJs<void>(
          rt, &T::close, jsInvoker_, instance_, std::move(code), std::move(reason), std::move(socketID));
    }
    void addListener(jsi::Runtime &rt, jsi::String eventName) override {
      static_assert(
          bridging::getParameterCount(&T::addListener) == 2,
          "Expected addListener(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::addListener, jsInvoker_, instance_, std::move(eventName));
    }
    void removeListeners(jsi::Runtime &rt, double count) override {
      static_assert(
          bridging::getParameterCount(&T::removeListeners) == 2,
          "Expected removeListeners(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::removeListeners, jsInvoker_, instance_, std::move(count));
    }

  private:
    friend class NativeWebSocketModuleCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeCPUTimeCxxSpecJSI : public TurboModule {
protected:
  NativeCPUTimeCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual double getCPUTimeNanos(jsi::Runtime &rt) = 0;
  virtual bool hasAccurateCPUTimeNanosForBenchmarks(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeCPUTimeCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "CPUTimeCxx";

protected:
  NativeCPUTimeCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeCPUTimeCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeCPUTimeCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeCPUTimeCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    double getCPUTimeNanos(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getCPUTimeNanos) == 1,
          "Expected getCPUTimeNanos(...) to have 1 parameters");

      return bridging::callFromJs<double>(
          rt, &T::getCPUTimeNanos, jsInvoker_, instance_);
    }
    bool hasAccurateCPUTimeNanosForBenchmarks(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::hasAccurateCPUTimeNanosForBenchmarks) == 1,
          "Expected hasAccurateCPUTimeNanosForBenchmarks(...) to have 1 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::hasAccurateCPUTimeNanosForBenchmarks, jsInvoker_, instance_);
    }

  private:
    friend class NativeCPUTimeCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


#pragma mark - NativeFantomNativeEventCategory

enum class NativeFantomNativeEventCategory { ContinuousStart, ContinuousEnd, Unspecified, Discrete, Continuous };

template <>
struct Bridging<NativeFantomNativeEventCategory> {
  static NativeFantomNativeEventCategory fromJs(jsi::Runtime &rt, const jsi::Value &rawValue) {
    double value = (double)rawValue.asNumber();
    if (value == 0) {
      return NativeFantomNativeEventCategory::ContinuousStart;
    } else if (value == 1) {
      return NativeFantomNativeEventCategory::ContinuousEnd;
    } else if (value == 2) {
      return NativeFantomNativeEventCategory::Unspecified;
    } else if (value == 3) {
      return NativeFantomNativeEventCategory::Discrete;
    } else if (value == 4) {
      return NativeFantomNativeEventCategory::Continuous;
    } else {
      throw jsi::JSError(rt, "No appropriate enum member found for value");
    }
  }

  static jsi::Value toJs(jsi::Runtime &rt, NativeFantomNativeEventCategory value) {
    if (value == NativeFantomNativeEventCategory::ContinuousStart) {
      return bridging::toJs(rt, 0);
    } else if (value == NativeFantomNativeEventCategory::ContinuousEnd) {
      return bridging::toJs(rt, 1);
    } else if (value == NativeFantomNativeEventCategory::Unspecified) {
      return bridging::toJs(rt, 2);
    } else if (value == NativeFantomNativeEventCategory::Discrete) {
      return bridging::toJs(rt, 3);
    } else if (value == NativeFantomNativeEventCategory::Continuous) {
      return bridging::toJs(rt, 4);
    } else {
      throw jsi::JSError(rt, "No appropriate enum member found for enum value");
    }
  }
};
  
#pragma mark - NativeFantomRenderFormatOptions

template <typename P0, typename P1>
struct NativeFantomRenderFormatOptions {
  P0 includeRoot;
  P1 includeLayoutMetrics;
  bool operator==(const NativeFantomRenderFormatOptions &other) const {
    return includeRoot == other.includeRoot && includeLayoutMetrics == other.includeLayoutMetrics;
  }
};

template <typename T>
struct NativeFantomRenderFormatOptionsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.includeRoot)>(rt, value.getProperty(rt, "includeRoot"), jsInvoker),
      bridging::fromJs<decltype(types.includeLayoutMetrics)>(rt, value.getProperty(rt, "includeLayoutMetrics"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static bool includeRootToJs(jsi::Runtime &rt, decltype(types.includeRoot) value) {
    return bridging::toJs(rt, value);
  }

  static bool includeLayoutMetricsToJs(jsi::Runtime &rt, decltype(types.includeLayoutMetrics) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "includeRoot", bridging::toJs(rt, value.includeRoot, jsInvoker));
    result.setProperty(rt, "includeLayoutMetrics", bridging::toJs(rt, value.includeLayoutMetrics, jsInvoker));
    return result;
  }
};



#pragma mark - NativeFantomScrollOptions

template <typename P0, typename P1, typename P2>
struct NativeFantomScrollOptions {
  P0 x;
  P1 y;
  P2 zoomScale;
  bool operator==(const NativeFantomScrollOptions &other) const {
    return x == other.x && y == other.y && zoomScale == other.zoomScale;
  }
};

template <typename T>
struct NativeFantomScrollOptionsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.x)>(rt, value.getProperty(rt, "x"), jsInvoker),
      bridging::fromJs<decltype(types.y)>(rt, value.getProperty(rt, "y"), jsInvoker),
      bridging::fromJs<decltype(types.zoomScale)>(rt, value.getProperty(rt, "zoomScale"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static double xToJs(jsi::Runtime &rt, decltype(types.x) value) {
    return bridging::toJs(rt, value);
  }

  static double yToJs(jsi::Runtime &rt, decltype(types.y) value) {
    return bridging::toJs(rt, value);
  }

  static double zoomScaleToJs(jsi::Runtime &rt, decltype(types.zoomScale) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "x", bridging::toJs(rt, value.x, jsInvoker));
    result.setProperty(rt, "y", bridging::toJs(rt, value.y, jsInvoker));
    if (value.zoomScale) {
      result.setProperty(rt, "zoomScale", bridging::toJs(rt, value.zoomScale.value(), jsInvoker));
    }
    return result;
  }
};

class JSI_EXPORT NativeFantomCxxSpecJSI : public TurboModule {
protected:
  NativeFantomCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void startSurface(jsi::Runtime &rt, double surfaceId, double viewportWidth, double viewportHeight, double devicePixelRatio) = 0;
  virtual void stopSurface(jsi::Runtime &rt, double surfaceId) = 0;
  virtual void enqueueNativeEvent(jsi::Runtime &rt, jsi::Value shadowNode, jsi::String type, std::optional<jsi::Value> payload, std::optional<jsi::Value> category, std::optional<bool> isUnique) = 0;
  virtual void scrollTo(jsi::Runtime &rt, jsi::Value shadowNode, jsi::Object options) = 0;
  virtual jsi::Array takeMountingManagerLogs(jsi::Runtime &rt, double surfaceId) = 0;
  virtual void flushMessageQueue(jsi::Runtime &rt) = 0;
  virtual void flushEventQueue(jsi::Runtime &rt) = 0;
  virtual void validateEmptyMessageQueue(jsi::Runtime &rt) = 0;
  virtual jsi::String getRenderedOutput(jsi::Runtime &rt, double surfaceId, jsi::Object config) = 0;
  virtual void reportTestSuiteResultsJSON(jsi::Runtime &rt, jsi::String results) = 0;
  virtual void saveJSMemoryHeapSnapshot(jsi::Runtime &rt, jsi::String filePath) = 0;

};

template <typename T>
class JSI_EXPORT NativeFantomCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "NativeFantomCxx";

protected:
  NativeFantomCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeFantomCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeFantomCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeFantomCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void startSurface(jsi::Runtime &rt, double surfaceId, double viewportWidth, double viewportHeight, double devicePixelRatio) override {
      static_assert(
          bridging::getParameterCount(&T::startSurface) == 5,
          "Expected startSurface(...) to have 5 parameters");

      return bridging::callFromJs<void>(
          rt, &T::startSurface, jsInvoker_, instance_, std::move(surfaceId), std::move(viewportWidth), std::move(viewportHeight), std::move(devicePixelRatio));
    }
    void stopSurface(jsi::Runtime &rt, double surfaceId) override {
      static_assert(
          bridging::getParameterCount(&T::stopSurface) == 2,
          "Expected stopSurface(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::stopSurface, jsInvoker_, instance_, std::move(surfaceId));
    }
    void enqueueNativeEvent(jsi::Runtime &rt, jsi::Value shadowNode, jsi::String type, std::optional<jsi::Value> payload, std::optional<jsi::Value> category, std::optional<bool> isUnique) override {
      static_assert(
          bridging::getParameterCount(&T::enqueueNativeEvent) == 6,
          "Expected enqueueNativeEvent(...) to have 6 parameters");

      return bridging::callFromJs<void>(
          rt, &T::enqueueNativeEvent, jsInvoker_, instance_, std::move(shadowNode), std::move(type), std::move(payload), std::move(category), std::move(isUnique));
    }
    void scrollTo(jsi::Runtime &rt, jsi::Value shadowNode, jsi::Object options) override {
      static_assert(
          bridging::getParameterCount(&T::scrollTo) == 3,
          "Expected scrollTo(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::scrollTo, jsInvoker_, instance_, std::move(shadowNode), std::move(options));
    }
    jsi::Array takeMountingManagerLogs(jsi::Runtime &rt, double surfaceId) override {
      static_assert(
          bridging::getParameterCount(&T::takeMountingManagerLogs) == 2,
          "Expected takeMountingManagerLogs(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::takeMountingManagerLogs, jsInvoker_, instance_, std::move(surfaceId));
    }
    void flushMessageQueue(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::flushMessageQueue) == 1,
          "Expected flushMessageQueue(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::flushMessageQueue, jsInvoker_, instance_);
    }
    void flushEventQueue(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::flushEventQueue) == 1,
          "Expected flushEventQueue(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::flushEventQueue, jsInvoker_, instance_);
    }
    void validateEmptyMessageQueue(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::validateEmptyMessageQueue) == 1,
          "Expected validateEmptyMessageQueue(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::validateEmptyMessageQueue, jsInvoker_, instance_);
    }
    jsi::String getRenderedOutput(jsi::Runtime &rt, double surfaceId, jsi::Object config) override {
      static_assert(
          bridging::getParameterCount(&T::getRenderedOutput) == 3,
          "Expected getRenderedOutput(...) to have 3 parameters");

      return bridging::callFromJs<jsi::String>(
          rt, &T::getRenderedOutput, jsInvoker_, instance_, std::move(surfaceId), std::move(config));
    }
    void reportTestSuiteResultsJSON(jsi::Runtime &rt, jsi::String results) override {
      static_assert(
          bridging::getParameterCount(&T::reportTestSuiteResultsJSON) == 2,
          "Expected reportTestSuiteResultsJSON(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::reportTestSuiteResultsJSON, jsInvoker_, instance_, std::move(results));
    }
    void saveJSMemoryHeapSnapshot(jsi::Runtime &rt, jsi::String filePath) override {
      static_assert(
          bridging::getParameterCount(&T::saveJSMemoryHeapSnapshot) == 2,
          "Expected saveJSMemoryHeapSnapshot(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::saveJSMemoryHeapSnapshot, jsInvoker_, instance_, std::move(filePath));
    }

  private:
    friend class NativeFantomCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeDOMCxxSpecJSI : public TurboModule {
protected:
  NativeDOMCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual double compareDocumentPosition(jsi::Runtime &rt, jsi::Value nativeNodeReference, jsi::Value otherNativeNodeReference) = 0;
  virtual jsi::Array getChildNodes(jsi::Runtime &rt, jsi::Value nativeNodeReference) = 0;
  virtual jsi::Value getParentNode(jsi::Runtime &rt, jsi::Value nativeNodeReference) = 0;
  virtual bool isConnected(jsi::Runtime &rt, jsi::Value nativeNodeReference) = 0;
  virtual jsi::Array getBorderWidth(jsi::Runtime &rt, jsi::Value nativeElementReference) = 0;
  virtual jsi::Array getBoundingClientRect(jsi::Runtime &rt, jsi::Value nativeElementReference, bool includeTransform) = 0;
  virtual jsi::Array getInnerSize(jsi::Runtime &rt, jsi::Value nativeElementReference) = 0;
  virtual jsi::Array getScrollPosition(jsi::Runtime &rt, jsi::Value nativeElementReference) = 0;
  virtual jsi::Array getScrollSize(jsi::Runtime &rt, jsi::Value nativeElementReference) = 0;
  virtual jsi::String getTagName(jsi::Runtime &rt, jsi::Value nativeElementReference) = 0;
  virtual jsi::String getTextContent(jsi::Runtime &rt, jsi::Value nativeElementReference) = 0;
  virtual bool hasPointerCapture(jsi::Runtime &rt, jsi::Value nativeElementReference, double pointerId) = 0;
  virtual void releasePointerCapture(jsi::Runtime &rt, jsi::Value nativeElementReference, double pointerId) = 0;
  virtual void setPointerCapture(jsi::Runtime &rt, jsi::Value nativeElementReference, double pointerId) = 0;
  virtual jsi::Array getOffset(jsi::Runtime &rt, jsi::Value nativeElementReference) = 0;
  virtual jsi::Value linkRootNode(jsi::Runtime &rt, double rootTag, jsi::Value instanceHandle) = 0;
  virtual void measure(jsi::Runtime &rt, jsi::Value nativeElementReference, jsi::Function callback) = 0;
  virtual void measureInWindow(jsi::Runtime &rt, jsi::Value nativeElementReference, jsi::Function callback) = 0;
  virtual void measureLayout(jsi::Runtime &rt, jsi::Value nativeElementReference, jsi::Value relativeNode, jsi::Function onFail, jsi::Function onSuccess) = 0;

};

template <typename T>
class JSI_EXPORT NativeDOMCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "NativeDOMCxx";

protected:
  NativeDOMCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeDOMCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeDOMCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeDOMCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    double compareDocumentPosition(jsi::Runtime &rt, jsi::Value nativeNodeReference, jsi::Value otherNativeNodeReference) override {
      static_assert(
          bridging::getParameterCount(&T::compareDocumentPosition) == 3,
          "Expected compareDocumentPosition(...) to have 3 parameters");

      return bridging::callFromJs<double>(
          rt, &T::compareDocumentPosition, jsInvoker_, instance_, std::move(nativeNodeReference), std::move(otherNativeNodeReference));
    }
    jsi::Array getChildNodes(jsi::Runtime &rt, jsi::Value nativeNodeReference) override {
      static_assert(
          bridging::getParameterCount(&T::getChildNodes) == 2,
          "Expected getChildNodes(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::getChildNodes, jsInvoker_, instance_, std::move(nativeNodeReference));
    }
    jsi::Value getParentNode(jsi::Runtime &rt, jsi::Value nativeNodeReference) override {
      static_assert(
          bridging::getParameterCount(&T::getParentNode) == 2,
          "Expected getParentNode(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::getParentNode, jsInvoker_, instance_, std::move(nativeNodeReference));
    }
    bool isConnected(jsi::Runtime &rt, jsi::Value nativeNodeReference) override {
      static_assert(
          bridging::getParameterCount(&T::isConnected) == 2,
          "Expected isConnected(...) to have 2 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::isConnected, jsInvoker_, instance_, std::move(nativeNodeReference));
    }
    jsi::Array getBorderWidth(jsi::Runtime &rt, jsi::Value nativeElementReference) override {
      static_assert(
          bridging::getParameterCount(&T::getBorderWidth) == 2,
          "Expected getBorderWidth(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::getBorderWidth, jsInvoker_, instance_, std::move(nativeElementReference));
    }
    jsi::Array getBoundingClientRect(jsi::Runtime &rt, jsi::Value nativeElementReference, bool includeTransform) override {
      static_assert(
          bridging::getParameterCount(&T::getBoundingClientRect) == 3,
          "Expected getBoundingClientRect(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::getBoundingClientRect, jsInvoker_, instance_, std::move(nativeElementReference), std::move(includeTransform));
    }
    jsi::Array getInnerSize(jsi::Runtime &rt, jsi::Value nativeElementReference) override {
      static_assert(
          bridging::getParameterCount(&T::getInnerSize) == 2,
          "Expected getInnerSize(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::getInnerSize, jsInvoker_, instance_, std::move(nativeElementReference));
    }
    jsi::Array getScrollPosition(jsi::Runtime &rt, jsi::Value nativeElementReference) override {
      static_assert(
          bridging::getParameterCount(&T::getScrollPosition) == 2,
          "Expected getScrollPosition(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::getScrollPosition, jsInvoker_, instance_, std::move(nativeElementReference));
    }
    jsi::Array getScrollSize(jsi::Runtime &rt, jsi::Value nativeElementReference) override {
      static_assert(
          bridging::getParameterCount(&T::getScrollSize) == 2,
          "Expected getScrollSize(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::getScrollSize, jsInvoker_, instance_, std::move(nativeElementReference));
    }
    jsi::String getTagName(jsi::Runtime &rt, jsi::Value nativeElementReference) override {
      static_assert(
          bridging::getParameterCount(&T::getTagName) == 2,
          "Expected getTagName(...) to have 2 parameters");

      return bridging::callFromJs<jsi::String>(
          rt, &T::getTagName, jsInvoker_, instance_, std::move(nativeElementReference));
    }
    jsi::String getTextContent(jsi::Runtime &rt, jsi::Value nativeElementReference) override {
      static_assert(
          bridging::getParameterCount(&T::getTextContent) == 2,
          "Expected getTextContent(...) to have 2 parameters");

      return bridging::callFromJs<jsi::String>(
          rt, &T::getTextContent, jsInvoker_, instance_, std::move(nativeElementReference));
    }
    bool hasPointerCapture(jsi::Runtime &rt, jsi::Value nativeElementReference, double pointerId) override {
      static_assert(
          bridging::getParameterCount(&T::hasPointerCapture) == 3,
          "Expected hasPointerCapture(...) to have 3 parameters");

      return bridging::callFromJs<bool>(
          rt, &T::hasPointerCapture, jsInvoker_, instance_, std::move(nativeElementReference), std::move(pointerId));
    }
    void releasePointerCapture(jsi::Runtime &rt, jsi::Value nativeElementReference, double pointerId) override {
      static_assert(
          bridging::getParameterCount(&T::releasePointerCapture) == 3,
          "Expected releasePointerCapture(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::releasePointerCapture, jsInvoker_, instance_, std::move(nativeElementReference), std::move(pointerId));
    }
    void setPointerCapture(jsi::Runtime &rt, jsi::Value nativeElementReference, double pointerId) override {
      static_assert(
          bridging::getParameterCount(&T::setPointerCapture) == 3,
          "Expected setPointerCapture(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::setPointerCapture, jsInvoker_, instance_, std::move(nativeElementReference), std::move(pointerId));
    }
    jsi::Array getOffset(jsi::Runtime &rt, jsi::Value nativeElementReference) override {
      static_assert(
          bridging::getParameterCount(&T::getOffset) == 2,
          "Expected getOffset(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::getOffset, jsInvoker_, instance_, std::move(nativeElementReference));
    }
    jsi::Value linkRootNode(jsi::Runtime &rt, double rootTag, jsi::Value instanceHandle) override {
      static_assert(
          bridging::getParameterCount(&T::linkRootNode) == 3,
          "Expected linkRootNode(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::linkRootNode, jsInvoker_, instance_, std::move(rootTag), std::move(instanceHandle));
    }
    void measure(jsi::Runtime &rt, jsi::Value nativeElementReference, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::measure) == 3,
          "Expected measure(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::measure, jsInvoker_, instance_, std::move(nativeElementReference), std::move(callback));
    }
    void measureInWindow(jsi::Runtime &rt, jsi::Value nativeElementReference, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::measureInWindow) == 3,
          "Expected measureInWindow(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::measureInWindow, jsInvoker_, instance_, std::move(nativeElementReference), std::move(callback));
    }
    void measureLayout(jsi::Runtime &rt, jsi::Value nativeElementReference, jsi::Value relativeNode, jsi::Function onFail, jsi::Function onSuccess) override {
      static_assert(
          bridging::getParameterCount(&T::measureLayout) == 5,
          "Expected measureLayout(...) to have 5 parameters");

      return bridging::callFromJs<void>(
          rt, &T::measureLayout, jsInvoker_, instance_, std::move(nativeElementReference), std::move(relativeNode), std::move(onFail), std::move(onSuccess));
    }

  private:
    friend class NativeDOMCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeIdleCallbacksIdleDeadline

template <typename P0, typename P1>
struct NativeIdleCallbacksIdleDeadline {
  P0 didTimeout;
  P1 timeRemaining;
  bool operator==(const NativeIdleCallbacksIdleDeadline &other) const {
    return didTimeout == other.didTimeout && timeRemaining == other.timeRemaining;
  }
};

template <typename T>
struct NativeIdleCallbacksIdleDeadlineBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.didTimeout)>(rt, value.getProperty(rt, "didTimeout"), jsInvoker),
      bridging::fromJs<decltype(types.timeRemaining)>(rt, value.getProperty(rt, "timeRemaining"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static bool didTimeoutToJs(jsi::Runtime &rt, decltype(types.didTimeout) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Function timeRemainingToJs(jsi::Runtime &rt, decltype(types.timeRemaining) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "didTimeout", bridging::toJs(rt, value.didTimeout, jsInvoker));
    result.setProperty(rt, "timeRemaining", bridging::toJs(rt, value.timeRemaining, jsInvoker));
    return result;
  }
};



#pragma mark - NativeIdleCallbacksRequestIdleCallbackOptions

template <typename P0>
struct NativeIdleCallbacksRequestIdleCallbackOptions {
  P0 timeout;
  bool operator==(const NativeIdleCallbacksRequestIdleCallbackOptions &other) const {
    return timeout == other.timeout;
  }
};

template <typename T>
struct NativeIdleCallbacksRequestIdleCallbackOptionsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.timeout)>(rt, value.getProperty(rt, "timeout"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static double timeoutToJs(jsi::Runtime &rt, decltype(types.timeout) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    if (value.timeout) {
      result.setProperty(rt, "timeout", bridging::toJs(rt, value.timeout.value(), jsInvoker));
    }
    return result;
  }
};

class JSI_EXPORT NativeIdleCallbacksCxxSpecJSI : public TurboModule {
protected:
  NativeIdleCallbacksCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual jsi::Value requestIdleCallback(jsi::Runtime &rt, jsi::Function callback, std::optional<jsi::Object> options) = 0;
  virtual void cancelIdleCallback(jsi::Runtime &rt, jsi::Value handle) = 0;

};

template <typename T>
class JSI_EXPORT NativeIdleCallbacksCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "NativeIdleCallbacksCxx";

protected:
  NativeIdleCallbacksCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeIdleCallbacksCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeIdleCallbacksCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeIdleCallbacksCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    jsi::Value requestIdleCallback(jsi::Runtime &rt, jsi::Function callback, std::optional<jsi::Object> options) override {
      static_assert(
          bridging::getParameterCount(&T::requestIdleCallback) == 3,
          "Expected requestIdleCallback(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::requestIdleCallback, jsInvoker_, instance_, std::move(callback), std::move(options));
    }
    void cancelIdleCallback(jsi::Runtime &rt, jsi::Value handle) override {
      static_assert(
          bridging::getParameterCount(&T::cancelIdleCallback) == 2,
          "Expected cancelIdleCallback(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::cancelIdleCallback, jsInvoker_, instance_, std::move(handle));
    }

  private:
    friend class NativeIdleCallbacksCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeIntersectionObserverNativeIntersectionObserverEntry

template <typename P0, typename P1, typename P2, typename P3, typename P4, typename P5, typename P6>
struct NativeIntersectionObserverNativeIntersectionObserverEntry {
  P0 intersectionObserverId;
  P1 targetInstanceHandle;
  P2 targetRect;
  P3 rootRect;
  P4 intersectionRect;
  P5 isIntersectingAboveThresholds;
  P6 time;
  bool operator==(const NativeIntersectionObserverNativeIntersectionObserverEntry &other) const {
    return intersectionObserverId == other.intersectionObserverId && targetInstanceHandle == other.targetInstanceHandle && targetRect == other.targetRect && rootRect == other.rootRect && intersectionRect == other.intersectionRect && isIntersectingAboveThresholds == other.isIntersectingAboveThresholds && time == other.time;
  }
};

template <typename T>
struct NativeIntersectionObserverNativeIntersectionObserverEntryBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.intersectionObserverId)>(rt, value.getProperty(rt, "intersectionObserverId"), jsInvoker),
      bridging::fromJs<decltype(types.targetInstanceHandle)>(rt, value.getProperty(rt, "targetInstanceHandle"), jsInvoker),
      bridging::fromJs<decltype(types.targetRect)>(rt, value.getProperty(rt, "targetRect"), jsInvoker),
      bridging::fromJs<decltype(types.rootRect)>(rt, value.getProperty(rt, "rootRect"), jsInvoker),
      bridging::fromJs<decltype(types.intersectionRect)>(rt, value.getProperty(rt, "intersectionRect"), jsInvoker),
      bridging::fromJs<decltype(types.isIntersectingAboveThresholds)>(rt, value.getProperty(rt, "isIntersectingAboveThresholds"), jsInvoker),
      bridging::fromJs<decltype(types.time)>(rt, value.getProperty(rt, "time"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static double intersectionObserverIdToJs(jsi::Runtime &rt, decltype(types.intersectionObserverId) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Value targetInstanceHandleToJs(jsi::Runtime &rt, decltype(types.targetInstanceHandle) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Array targetRectToJs(jsi::Runtime &rt, decltype(types.targetRect) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Array rootRectToJs(jsi::Runtime &rt, decltype(types.rootRect) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::Array> intersectionRectToJs(jsi::Runtime &rt, decltype(types.intersectionRect) value) {
    return bridging::toJs(rt, value);
  }

  static bool isIntersectingAboveThresholdsToJs(jsi::Runtime &rt, decltype(types.isIntersectingAboveThresholds) value) {
    return bridging::toJs(rt, value);
  }

  static double timeToJs(jsi::Runtime &rt, decltype(types.time) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "intersectionObserverId", bridging::toJs(rt, value.intersectionObserverId, jsInvoker));
    result.setProperty(rt, "targetInstanceHandle", bridging::toJs(rt, value.targetInstanceHandle, jsInvoker));
    result.setProperty(rt, "targetRect", bridging::toJs(rt, value.targetRect, jsInvoker));
    result.setProperty(rt, "rootRect", bridging::toJs(rt, value.rootRect, jsInvoker));
    result.setProperty(rt, "intersectionRect", bridging::toJs(rt, value.intersectionRect, jsInvoker));
    result.setProperty(rt, "isIntersectingAboveThresholds", bridging::toJs(rt, value.isIntersectingAboveThresholds, jsInvoker));
    result.setProperty(rt, "time", bridging::toJs(rt, value.time, jsInvoker));
    return result;
  }
};



#pragma mark - NativeIntersectionObserverNativeIntersectionObserverObserveOptions

template <typename P0, typename P1, typename P2, typename P3>
struct NativeIntersectionObserverNativeIntersectionObserverObserveOptions {
  P0 intersectionObserverId;
  P1 targetShadowNode;
  P2 thresholds;
  P3 rootThresholds;
  bool operator==(const NativeIntersectionObserverNativeIntersectionObserverObserveOptions &other) const {
    return intersectionObserverId == other.intersectionObserverId && targetShadowNode == other.targetShadowNode && thresholds == other.thresholds && rootThresholds == other.rootThresholds;
  }
};

template <typename T>
struct NativeIntersectionObserverNativeIntersectionObserverObserveOptionsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.intersectionObserverId)>(rt, value.getProperty(rt, "intersectionObserverId"), jsInvoker),
      bridging::fromJs<decltype(types.targetShadowNode)>(rt, value.getProperty(rt, "targetShadowNode"), jsInvoker),
      bridging::fromJs<decltype(types.thresholds)>(rt, value.getProperty(rt, "thresholds"), jsInvoker),
      bridging::fromJs<decltype(types.rootThresholds)>(rt, value.getProperty(rt, "rootThresholds"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static double intersectionObserverIdToJs(jsi::Runtime &rt, decltype(types.intersectionObserverId) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Value targetShadowNodeToJs(jsi::Runtime &rt, decltype(types.targetShadowNode) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Array thresholdsToJs(jsi::Runtime &rt, decltype(types.thresholds) value) {
    return bridging::toJs(rt, value);
  }

  static std::optional<jsi::Array> rootThresholdsToJs(jsi::Runtime &rt, decltype(types.rootThresholds) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "intersectionObserverId", bridging::toJs(rt, value.intersectionObserverId, jsInvoker));
    result.setProperty(rt, "targetShadowNode", bridging::toJs(rt, value.targetShadowNode, jsInvoker));
    result.setProperty(rt, "thresholds", bridging::toJs(rt, value.thresholds, jsInvoker));
    if (value.rootThresholds) {
      result.setProperty(rt, "rootThresholds", bridging::toJs(rt, value.rootThresholds.value(), jsInvoker));
    }
    return result;
  }
};

class JSI_EXPORT NativeIntersectionObserverCxxSpecJSI : public TurboModule {
protected:
  NativeIntersectionObserverCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void observe(jsi::Runtime &rt, jsi::Object options) = 0;
  virtual void unobserve(jsi::Runtime &rt, double intersectionObserverId, jsi::Value targetShadowNode) = 0;
  virtual void connect(jsi::Runtime &rt, jsi::Function notifyIntersectionObserversCallback) = 0;
  virtual void disconnect(jsi::Runtime &rt) = 0;
  virtual jsi::Array takeRecords(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeIntersectionObserverCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "NativeIntersectionObserverCxx";

protected:
  NativeIntersectionObserverCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeIntersectionObserverCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeIntersectionObserverCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeIntersectionObserverCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void observe(jsi::Runtime &rt, jsi::Object options) override {
      static_assert(
          bridging::getParameterCount(&T::observe) == 2,
          "Expected observe(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::observe, jsInvoker_, instance_, std::move(options));
    }
    void unobserve(jsi::Runtime &rt, double intersectionObserverId, jsi::Value targetShadowNode) override {
      static_assert(
          bridging::getParameterCount(&T::unobserve) == 3,
          "Expected unobserve(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::unobserve, jsInvoker_, instance_, std::move(intersectionObserverId), std::move(targetShadowNode));
    }
    void connect(jsi::Runtime &rt, jsi::Function notifyIntersectionObserversCallback) override {
      static_assert(
          bridging::getParameterCount(&T::connect) == 2,
          "Expected connect(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::connect, jsInvoker_, instance_, std::move(notifyIntersectionObserversCallback));
    }
    void disconnect(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::disconnect) == 1,
          "Expected disconnect(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::disconnect, jsInvoker_, instance_);
    }
    jsi::Array takeRecords(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::takeRecords) == 1,
          "Expected takeRecords(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::takeRecords, jsInvoker_, instance_);
    }

  private:
    friend class NativeIntersectionObserverCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  class JSI_EXPORT NativeMicrotasksCxxSpecJSI : public TurboModule {
protected:
  NativeMicrotasksCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void queueMicrotask(jsi::Runtime &rt, jsi::Function callback) = 0;

};

template <typename T>
class JSI_EXPORT NativeMicrotasksCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "NativeMicrotasksCxx";

protected:
  NativeMicrotasksCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeMicrotasksCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeMicrotasksCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeMicrotasksCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void queueMicrotask(jsi::Runtime &rt, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::queueMicrotask) == 2,
          "Expected queueMicrotask(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::queueMicrotask, jsInvoker_, instance_, std::move(callback));
    }

  private:
    friend class NativeMicrotasksCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  
#pragma mark - NativeMutationObserverNativeMutationObserverObserveOptions

template <typename P0, typename P1, typename P2>
struct NativeMutationObserverNativeMutationObserverObserveOptions {
  P0 mutationObserverId;
  P1 targetShadowNode;
  P2 subtree;
  bool operator==(const NativeMutationObserverNativeMutationObserverObserveOptions &other) const {
    return mutationObserverId == other.mutationObserverId && targetShadowNode == other.targetShadowNode && subtree == other.subtree;
  }
};

template <typename T>
struct NativeMutationObserverNativeMutationObserverObserveOptionsBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.mutationObserverId)>(rt, value.getProperty(rt, "mutationObserverId"), jsInvoker),
      bridging::fromJs<decltype(types.targetShadowNode)>(rt, value.getProperty(rt, "targetShadowNode"), jsInvoker),
      bridging::fromJs<decltype(types.subtree)>(rt, value.getProperty(rt, "subtree"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static double mutationObserverIdToJs(jsi::Runtime &rt, decltype(types.mutationObserverId) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Value targetShadowNodeToJs(jsi::Runtime &rt, decltype(types.targetShadowNode) value) {
    return bridging::toJs(rt, value);
  }

  static bool subtreeToJs(jsi::Runtime &rt, decltype(types.subtree) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "mutationObserverId", bridging::toJs(rt, value.mutationObserverId, jsInvoker));
    result.setProperty(rt, "targetShadowNode", bridging::toJs(rt, value.targetShadowNode, jsInvoker));
    result.setProperty(rt, "subtree", bridging::toJs(rt, value.subtree, jsInvoker));
    return result;
  }
};



#pragma mark - NativeMutationObserverNativeMutationRecord

template <typename P0, typename P1, typename P2, typename P3>
struct NativeMutationObserverNativeMutationRecord {
  P0 mutationObserverId;
  P1 target;
  P2 addedNodes;
  P3 removedNodes;
  bool operator==(const NativeMutationObserverNativeMutationRecord &other) const {
    return mutationObserverId == other.mutationObserverId && target == other.target && addedNodes == other.addedNodes && removedNodes == other.removedNodes;
  }
};

template <typename T>
struct NativeMutationObserverNativeMutationRecordBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.mutationObserverId)>(rt, value.getProperty(rt, "mutationObserverId"), jsInvoker),
      bridging::fromJs<decltype(types.target)>(rt, value.getProperty(rt, "target"), jsInvoker),
      bridging::fromJs<decltype(types.addedNodes)>(rt, value.getProperty(rt, "addedNodes"), jsInvoker),
      bridging::fromJs<decltype(types.removedNodes)>(rt, value.getProperty(rt, "removedNodes"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static double mutationObserverIdToJs(jsi::Runtime &rt, decltype(types.mutationObserverId) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Value targetToJs(jsi::Runtime &rt, decltype(types.target) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Array addedNodesToJs(jsi::Runtime &rt, decltype(types.addedNodes) value) {
    return bridging::toJs(rt, value);
  }

  static jsi::Array removedNodesToJs(jsi::Runtime &rt, decltype(types.removedNodes) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "mutationObserverId", bridging::toJs(rt, value.mutationObserverId, jsInvoker));
    result.setProperty(rt, "target", bridging::toJs(rt, value.target, jsInvoker));
    result.setProperty(rt, "addedNodes", bridging::toJs(rt, value.addedNodes, jsInvoker));
    result.setProperty(rt, "removedNodes", bridging::toJs(rt, value.removedNodes, jsInvoker));
    return result;
  }
};

class JSI_EXPORT NativeMutationObserverCxxSpecJSI : public TurboModule {
protected:
  NativeMutationObserverCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual void observe(jsi::Runtime &rt, jsi::Object options) = 0;
  virtual void unobserve(jsi::Runtime &rt, double mutationObserverId, jsi::Value targetShadowNode) = 0;
  virtual void connect(jsi::Runtime &rt, jsi::Function notifyMutationObservers, jsi::Function getPublicInstanceFromInstanceHandle) = 0;
  virtual void disconnect(jsi::Runtime &rt) = 0;
  virtual jsi::Array takeRecords(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativeMutationObserverCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "NativeMutationObserverCxx";

protected:
  NativeMutationObserverCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativeMutationObserverCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativeMutationObserverCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativeMutationObserverCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    void observe(jsi::Runtime &rt, jsi::Object options) override {
      static_assert(
          bridging::getParameterCount(&T::observe) == 2,
          "Expected observe(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::observe, jsInvoker_, instance_, std::move(options));
    }
    void unobserve(jsi::Runtime &rt, double mutationObserverId, jsi::Value targetShadowNode) override {
      static_assert(
          bridging::getParameterCount(&T::unobserve) == 3,
          "Expected unobserve(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::unobserve, jsInvoker_, instance_, std::move(mutationObserverId), std::move(targetShadowNode));
    }
    void connect(jsi::Runtime &rt, jsi::Function notifyMutationObservers, jsi::Function getPublicInstanceFromInstanceHandle) override {
      static_assert(
          bridging::getParameterCount(&T::connect) == 3,
          "Expected connect(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::connect, jsInvoker_, instance_, std::move(notifyMutationObservers), std::move(getPublicInstanceFromInstanceHandle));
    }
    void disconnect(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::disconnect) == 1,
          "Expected disconnect(...) to have 1 parameters");

      return bridging::callFromJs<void>(
          rt, &T::disconnect, jsInvoker_, instance_);
    }
    jsi::Array takeRecords(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::takeRecords) == 1,
          "Expected takeRecords(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::takeRecords, jsInvoker_, instance_);
    }

  private:
    friend class NativeMutationObserverCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};


  

#pragma mark - NativePerformancePerformanceObserverInit

template <typename P0, typename P1, typename P2, typename P3>
struct NativePerformancePerformanceObserverInit {
  P0 entryTypes;
  P1 type;
  P2 buffered;
  P3 durationThreshold;
  bool operator==(const NativePerformancePerformanceObserverInit &other) const {
    return entryTypes == other.entryTypes && type == other.type && buffered == other.buffered && durationThreshold == other.durationThreshold;
  }
};

template <typename T>
struct NativePerformancePerformanceObserverInitBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.entryTypes)>(rt, value.getProperty(rt, "entryTypes"), jsInvoker),
      bridging::fromJs<decltype(types.type)>(rt, value.getProperty(rt, "type"), jsInvoker),
      bridging::fromJs<decltype(types.buffered)>(rt, value.getProperty(rt, "buffered"), jsInvoker),
      bridging::fromJs<decltype(types.durationThreshold)>(rt, value.getProperty(rt, "durationThreshold"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::Array entryTypesToJs(jsi::Runtime &rt, decltype(types.entryTypes) value) {
    return bridging::toJs(rt, value);
  }

  static double typeToJs(jsi::Runtime &rt, decltype(types.type) value) {
    return bridging::toJs(rt, value);
  }

  static bool bufferedToJs(jsi::Runtime &rt, decltype(types.buffered) value) {
    return bridging::toJs(rt, value);
  }

  static double durationThresholdToJs(jsi::Runtime &rt, decltype(types.durationThreshold) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    if (value.entryTypes) {
      result.setProperty(rt, "entryTypes", bridging::toJs(rt, value.entryTypes.value(), jsInvoker));
    }
    if (value.type) {
      result.setProperty(rt, "type", bridging::toJs(rt, value.type.value(), jsInvoker));
    }
    if (value.buffered) {
      result.setProperty(rt, "buffered", bridging::toJs(rt, value.buffered.value(), jsInvoker));
    }
    if (value.durationThreshold) {
      result.setProperty(rt, "durationThreshold", bridging::toJs(rt, value.durationThreshold.value(), jsInvoker));
    }
    return result;
  }
};



#pragma mark - NativePerformanceRawPerformanceEntry

template <typename P0, typename P1, typename P2, typename P3, typename P4, typename P5, typename P6>
struct NativePerformanceRawPerformanceEntry {
  P0 name;
  P1 entryType;
  P2 startTime;
  P3 duration;
  P4 processingStart;
  P5 processingEnd;
  P6 interactionId;
  bool operator==(const NativePerformanceRawPerformanceEntry &other) const {
    return name == other.name && entryType == other.entryType && startTime == other.startTime && duration == other.duration && processingStart == other.processingStart && processingEnd == other.processingEnd && interactionId == other.interactionId;
  }
};

template <typename T>
struct NativePerformanceRawPerformanceEntryBridging {
  static T types;

  static T fromJs(
      jsi::Runtime &rt,
      const jsi::Object &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    T result{
      bridging::fromJs<decltype(types.name)>(rt, value.getProperty(rt, "name"), jsInvoker),
      bridging::fromJs<decltype(types.entryType)>(rt, value.getProperty(rt, "entryType"), jsInvoker),
      bridging::fromJs<decltype(types.startTime)>(rt, value.getProperty(rt, "startTime"), jsInvoker),
      bridging::fromJs<decltype(types.duration)>(rt, value.getProperty(rt, "duration"), jsInvoker),
      bridging::fromJs<decltype(types.processingStart)>(rt, value.getProperty(rt, "processingStart"), jsInvoker),
      bridging::fromJs<decltype(types.processingEnd)>(rt, value.getProperty(rt, "processingEnd"), jsInvoker),
      bridging::fromJs<decltype(types.interactionId)>(rt, value.getProperty(rt, "interactionId"), jsInvoker)};
    return result;
  }

#ifdef DEBUG
  static jsi::String nameToJs(jsi::Runtime &rt, decltype(types.name) value) {
    return bridging::toJs(rt, value);
  }

  static double entryTypeToJs(jsi::Runtime &rt, decltype(types.entryType) value) {
    return bridging::toJs(rt, value);
  }

  static double startTimeToJs(jsi::Runtime &rt, decltype(types.startTime) value) {
    return bridging::toJs(rt, value);
  }

  static double durationToJs(jsi::Runtime &rt, decltype(types.duration) value) {
    return bridging::toJs(rt, value);
  }

  static double processingStartToJs(jsi::Runtime &rt, decltype(types.processingStart) value) {
    return bridging::toJs(rt, value);
  }

  static double processingEndToJs(jsi::Runtime &rt, decltype(types.processingEnd) value) {
    return bridging::toJs(rt, value);
  }

  static double interactionIdToJs(jsi::Runtime &rt, decltype(types.interactionId) value) {
    return bridging::toJs(rt, value);
  }
#endif

  static jsi::Object toJs(
      jsi::Runtime &rt,
      const T &value,
      const std::shared_ptr<CallInvoker> &jsInvoker) {
    auto result = facebook::jsi::Object(rt);
    result.setProperty(rt, "name", bridging::toJs(rt, value.name, jsInvoker));
    result.setProperty(rt, "entryType", bridging::toJs(rt, value.entryType, jsInvoker));
    result.setProperty(rt, "startTime", bridging::toJs(rt, value.startTime, jsInvoker));
    result.setProperty(rt, "duration", bridging::toJs(rt, value.duration, jsInvoker));
    if (value.processingStart) {
      result.setProperty(rt, "processingStart", bridging::toJs(rt, value.processingStart.value(), jsInvoker));
    }
    if (value.processingEnd) {
      result.setProperty(rt, "processingEnd", bridging::toJs(rt, value.processingEnd.value(), jsInvoker));
    }
    if (value.interactionId) {
      result.setProperty(rt, "interactionId", bridging::toJs(rt, value.interactionId.value(), jsInvoker));
    }
    return result;
  }
};


class JSI_EXPORT NativePerformanceCxxSpecJSI : public TurboModule {
protected:
  NativePerformanceCxxSpecJSI(std::shared_ptr<CallInvoker> jsInvoker);

public:
  virtual double now(jsi::Runtime &rt) = 0;
  virtual double markWithResult(jsi::Runtime &rt, jsi::String name, std::optional<double> startTime) = 0;
  virtual jsi::Array measureWithResult(jsi::Runtime &rt, jsi::String name, double startTime, double endTime, std::optional<double> duration, std::optional<jsi::String> startMark, std::optional<jsi::String> endMark) = 0;
  virtual void clearMarks(jsi::Runtime &rt, std::optional<jsi::String> entryName) = 0;
  virtual void clearMeasures(jsi::Runtime &rt, std::optional<jsi::String> entryName) = 0;
  virtual jsi::Array getEntries(jsi::Runtime &rt) = 0;
  virtual jsi::Array getEntriesByName(jsi::Runtime &rt, jsi::String entryName, std::optional<double> entryType) = 0;
  virtual jsi::Array getEntriesByType(jsi::Runtime &rt, double entryType) = 0;
  virtual jsi::Array getEventCounts(jsi::Runtime &rt) = 0;
  virtual jsi::Object getSimpleMemoryInfo(jsi::Runtime &rt) = 0;
  virtual jsi::Object getReactNativeStartupTiming(jsi::Runtime &rt) = 0;
  virtual jsi::Value createObserver(jsi::Runtime &rt, jsi::Function callback) = 0;
  virtual double getDroppedEntriesCount(jsi::Runtime &rt, jsi::Value observer) = 0;
  virtual void observe(jsi::Runtime &rt, jsi::Value observer, jsi::Object options) = 0;
  virtual void disconnect(jsi::Runtime &rt, jsi::Value observer) = 0;
  virtual jsi::Array takeRecords(jsi::Runtime &rt, jsi::Value observer, bool sort) = 0;
  virtual jsi::Array getSupportedPerformanceEntryTypes(jsi::Runtime &rt) = 0;

};

template <typename T>
class JSI_EXPORT NativePerformanceCxxSpec : public TurboModule {
public:
  jsi::Value create(jsi::Runtime &rt, const jsi::PropNameID &propName) override {
    return delegate_.create(rt, propName);
  }

  std::vector<jsi::PropNameID> getPropertyNames(jsi::Runtime& runtime) override {
    return delegate_.getPropertyNames(runtime);
  }

  static constexpr std::string_view kModuleName = "NativePerformanceCxx";

protected:
  NativePerformanceCxxSpec(std::shared_ptr<CallInvoker> jsInvoker)
    : TurboModule(std::string{NativePerformanceCxxSpec::kModuleName}, jsInvoker),
      delegate_(reinterpret_cast<T*>(this), jsInvoker) {}


private:
  class Delegate : public NativePerformanceCxxSpecJSI {
  public:
    Delegate(T *instance, std::shared_ptr<CallInvoker> jsInvoker) :
      NativePerformanceCxxSpecJSI(std::move(jsInvoker)), instance_(instance) {

    }

    double now(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::now) == 1,
          "Expected now(...) to have 1 parameters");

      return bridging::callFromJs<double>(
          rt, &T::now, jsInvoker_, instance_);
    }
    double markWithResult(jsi::Runtime &rt, jsi::String name, std::optional<double> startTime) override {
      static_assert(
          bridging::getParameterCount(&T::markWithResult) == 3,
          "Expected markWithResult(...) to have 3 parameters");

      return bridging::callFromJs<double>(
          rt, &T::markWithResult, jsInvoker_, instance_, std::move(name), std::move(startTime));
    }
    jsi::Array measureWithResult(jsi::Runtime &rt, jsi::String name, double startTime, double endTime, std::optional<double> duration, std::optional<jsi::String> startMark, std::optional<jsi::String> endMark) override {
      static_assert(
          bridging::getParameterCount(&T::measureWithResult) == 7,
          "Expected measureWithResult(...) to have 7 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::measureWithResult, jsInvoker_, instance_, std::move(name), std::move(startTime), std::move(endTime), std::move(duration), std::move(startMark), std::move(endMark));
    }
    void clearMarks(jsi::Runtime &rt, std::optional<jsi::String> entryName) override {
      static_assert(
          bridging::getParameterCount(&T::clearMarks) == 2,
          "Expected clearMarks(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::clearMarks, jsInvoker_, instance_, std::move(entryName));
    }
    void clearMeasures(jsi::Runtime &rt, std::optional<jsi::String> entryName) override {
      static_assert(
          bridging::getParameterCount(&T::clearMeasures) == 2,
          "Expected clearMeasures(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::clearMeasures, jsInvoker_, instance_, std::move(entryName));
    }
    jsi::Array getEntries(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getEntries) == 1,
          "Expected getEntries(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::getEntries, jsInvoker_, instance_);
    }
    jsi::Array getEntriesByName(jsi::Runtime &rt, jsi::String entryName, std::optional<double> entryType) override {
      static_assert(
          bridging::getParameterCount(&T::getEntriesByName) == 3,
          "Expected getEntriesByName(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::getEntriesByName, jsInvoker_, instance_, std::move(entryName), std::move(entryType));
    }
    jsi::Array getEntriesByType(jsi::Runtime &rt, double entryType) override {
      static_assert(
          bridging::getParameterCount(&T::getEntriesByType) == 2,
          "Expected getEntriesByType(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::getEntriesByType, jsInvoker_, instance_, std::move(entryType));
    }
    jsi::Array getEventCounts(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getEventCounts) == 1,
          "Expected getEventCounts(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::getEventCounts, jsInvoker_, instance_);
    }
    jsi::Object getSimpleMemoryInfo(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getSimpleMemoryInfo) == 1,
          "Expected getSimpleMemoryInfo(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getSimpleMemoryInfo, jsInvoker_, instance_);
    }
    jsi::Object getReactNativeStartupTiming(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getReactNativeStartupTiming) == 1,
          "Expected getReactNativeStartupTiming(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Object>(
          rt, &T::getReactNativeStartupTiming, jsInvoker_, instance_);
    }
    jsi::Value createObserver(jsi::Runtime &rt, jsi::Function callback) override {
      static_assert(
          bridging::getParameterCount(&T::createObserver) == 2,
          "Expected createObserver(...) to have 2 parameters");

      return bridging::callFromJs<jsi::Value>(
          rt, &T::createObserver, jsInvoker_, instance_, std::move(callback));
    }
    double getDroppedEntriesCount(jsi::Runtime &rt, jsi::Value observer) override {
      static_assert(
          bridging::getParameterCount(&T::getDroppedEntriesCount) == 2,
          "Expected getDroppedEntriesCount(...) to have 2 parameters");

      return bridging::callFromJs<double>(
          rt, &T::getDroppedEntriesCount, jsInvoker_, instance_, std::move(observer));
    }
    void observe(jsi::Runtime &rt, jsi::Value observer, jsi::Object options) override {
      static_assert(
          bridging::getParameterCount(&T::observe) == 3,
          "Expected observe(...) to have 3 parameters");

      return bridging::callFromJs<void>(
          rt, &T::observe, jsInvoker_, instance_, std::move(observer), std::move(options));
    }
    void disconnect(jsi::Runtime &rt, jsi::Value observer) override {
      static_assert(
          bridging::getParameterCount(&T::disconnect) == 2,
          "Expected disconnect(...) to have 2 parameters");

      return bridging::callFromJs<void>(
          rt, &T::disconnect, jsInvoker_, instance_, std::move(observer));
    }
    jsi::Array takeRecords(jsi::Runtime &rt, jsi::Value observer, bool sort) override {
      static_assert(
          bridging::getParameterCount(&T::takeRecords) == 3,
          "Expected takeRecords(...) to have 3 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::takeRecords, jsInvoker_, instance_, std::move(observer), std::move(sort));
    }
    jsi::Array getSupportedPerformanceEntryTypes(jsi::Runtime &rt) override {
      static_assert(
          bridging::getParameterCount(&T::getSupportedPerformanceEntryTypes) == 1,
          "Expected getSupportedPerformanceEntryTypes(...) to have 1 parameters");

      return bridging::callFromJs<jsi::Array>(
          rt, &T::getSupportedPerformanceEntryTypes, jsInvoker_, instance_);
    }

  private:
    friend class NativePerformanceCxxSpec;
    T *instance_;
  };

  Delegate delegate_;
};

} // namespace facebook::react
